# General Instructions on how to interact with this repository

Your goal is to 


## Instructions to rewrite scripts

1. Each type of script has to be rewritten in a certain way. This means behaviors and listeners will need to be written differently. same applies to validators and conditions. (validators and conditions are written the same way, with jira expressions).
2. Behaviors need to be translated from groovy to javascript. More instructions below. Document named behaviors.md
3. Listeners must be translated to normal groovy code but using unirest, can't use the normal groovy classes and also can't import anything to the code. Listeners have specific documentation wich is a file named "knowledge base/listeners.md".
4. Jira expressions is named "knowledge base/jxp.md" (this serves to conditions and validators).
5. Scripted fields must return a value, the logic is the same, no imports, only code using unirest. 
6. Example code will be provided in a csv named "knowledge base/examples.csv". with the columns (script name, type, description, script server, script cloud)
7. The files to be edited will be on the folder "source".
8. All the scripts rewritten need to be added to a column named "Script Cloud" (if this column does not exist on the files in the source folder, feel free to add it.)
9. To do the post functions you can use the same reference as the listeners, but without the context events. The only event variable you can use are the issue.

## How to handle files
1. Read the files from "knowledge base" for context - if needed.
2. Read and Write to files from folder "source". Not editing anything to any column besides the "Cloud Code" columns.
3. NEVER edit files in the knowledge base. Only if requested to.
4. Never edit files terminated in .md

## Your task
To rewrite code from Scriptrunner for Jira Server to Scriptrunner for Jira Cloud. Some functionality will be needed to be written as javascript (behaviors) some as groovy without imports (listeners and postfunctions) and some will be needed to be trasnlated to boolean expressions without imports and without function or variable declaration, so called Jira Expressions (jxp).

## How to start?
Create a checklist.txt file and add (1 to each line)
[] behaviors
[] listeners
[] workflow postfunctions
[] workflow validators (jxp)
[] workflow conditions (jxp)
[] scripted fields

As you finish the scripts on each category, mark it as a x

such as [x]


