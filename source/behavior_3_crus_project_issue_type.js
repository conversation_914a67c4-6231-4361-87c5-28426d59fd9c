/*
Creator: <PERSON>pose: CRUS project - Client Reporting - USGH - Hide fields based on Issue Type
Change log: Rewritten for Jira Cloud compatibility
*/

// Access the context for the UI Modification
const context = await getContext();
const issueType = context.extension.issueType.name;

console.log("Behaviours CRUS Project – Issue Type started.");
console.log("Issue Type:", issueType);

// Get field references with proper custom field IDs
const requestType = getFieldById("customfield_10080"); // Field (Request Type)
const newOrEnhancement = getFieldById("customfield_10081"); // Field (New or Enhancement)
const standardDataExtractType = getFieldById("customfield_10082"); // Field (Standard Data Extract Type)
const reportName = getFieldById("customfield_10083"); // Field (Report Name)
const description = getFieldById("description"); // Field (Description)
const businessRequirements = getFieldById("customfield_10084"); // Field (Business Requirements)
const areThereAnyTags = getFieldById("customfield_10085"); // Field (Are there any tags?)
const clientName = getFieldById("customfield_10086"); // Field (Client Name)
const clientLaunchDate = getFieldById("customfield_10087"); // Field (Client Launch Date)
const contractLanguageForGrievanceReporting = getFieldById("customfield_10088"); // Field (Contract Language for Grievance Reporting)
const clientLevel = getFieldById("customfield_10089"); // Field (Client Level)
const sensitiveInformation = getFieldById("customfield_10090"); // Field (Sensitive Information)
const fileTypeDelimiter = getFieldById("customfield_10091"); // Field (File Type Delimiter)

switch(issueType) {
    case "Task":
        reportName.setVisible(false);
        reportName.setRequired(false);
        description.setVisible(true);
        description.setRequired(true);
        clientLevel.setRequired(false);
        sensitiveInformation.setVisible(true);
        sensitiveInformation.setRequired(false);
        fileTypeDelimiter.setVisible(true);
        break;
        
    case "Story":
        requestType.setRequired(true);
        newOrEnhancement.setVisible(false);
        standardDataExtractType.setVisible(false);
        standardDataExtractType.setRequired(false);
        areThereAnyTags.setVisible(false);
        areThereAnyTags.setRequired(false);
        clientName.setVisible(false);
        clientName.setRequired(false);
        clientLaunchDate.setRequired(false);
        contractLanguageForGrievanceReporting.setVisible(false);
        contractLanguageForGrievanceReporting.setRequired(false);
        description.setVisible(true);
        description.setRequired(true);
        businessRequirements.setRequired(true);
        break;
        
    case "Bug":
        reportName.setVisible(true);
        reportName.setRequired(true);
        description.setVisible(true);
        description.setRequired(false);
        clientLevel.setRequired(false);
        sensitiveInformation.setVisible(true);
        sensitiveInformation.setRequired(false);
        fileTypeDelimiter.setVisible(false);
        break;
        
    default:
        break;
}

console.log("Behaviours CRUS Project – Issue Type completed.");
