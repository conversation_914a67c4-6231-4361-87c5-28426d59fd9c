/*
Creator: <PERSON>: CRUS project - Client Reporting - USGH - Hide fields based on New or Enhancement
Change log: Rewritten for Jira Cloud compatibility
*/

// Check if New or Enhancement field has changed
if (getChangeField().getName() === "New or Enhancement") {
    console.log("Behaviours CRUS Project – New or Enhancement started.");
    
    const newOrEnhancementValue = getChangeField().getValue().value;
    console.log("New or Enhancement:", newOrEnhancementValue);
    
    // Get field references with proper custom field IDs
    const standardDataExtractType = getFieldById("customfield_10082"); // Field (Standard Data Extract Type)
    const areThereAnyTags = getFieldById("customfield_10085"); // Field (Are there any tags?)
    const clientName = getFieldById("customfield_10086"); // Field (Client Name)
    const clientLaunchDate = getFieldById("customfield_10087"); // Field (Client Launch Date)
    const contractLanguageForGrievanceReporting = getFieldById("customfield_10088"); // Field (Contract Language for Grievance Reporting)
    const clientLevel = getFieldById("customfield_10089"); // Field (Client Level)
    const businessRequirements = getFieldById("customfield_10084"); // Field (Business Requirements)
    const isThisReusable = getFieldById("customfield_10090"); // Field (Is this Reusable?)
    const reportingTeamOwner = getFieldById("customfield_10091"); // Field (Reporting Team Owner)
    const approver = getFieldById("customfield_10092"); // Field (Approver)
    const audience = getFieldById("customfield_10093"); // Field (Audience (SL))
    const audienceDetails = getFieldById("customfield_10094"); // Field (Audience Details)
    const contractualObligation = getFieldById("customfield_10095"); // Field (Contractual Obligation)
    const sOWorBRDRequired = getFieldById("customfield_10096"); // Field (SOW or BRD required?)
    const sensitiveInformation = getFieldById("customfield_10097"); // Field (Sensitive Information)
    const fileTypePGPEncryption = getFieldById("customfield_10098"); // Field (File Type PGP Encryption)
    const fileTypeExtension = getFieldById("customfield_10099"); // Field (File Type Extension)
    const fileTypeDelimiter = getFieldById("customfield_10100"); // Field (File Type Delimiter)
    const extensionDetails = getFieldById("customfield_10101"); // Field (Extension Details)
    const frequency = getFieldById("customfield_10102"); // Field (Frequency)
    const modeOfDelivery = getFieldById("customfield_10103"); // Field (Mode of Delivery)
    const modeOfDeliveryVendor = getFieldById("customfield_10104"); // Field (SFTP - Client/Vendor Details)
    const modeOfDeliveryEmail = getFieldById("customfield_10105"); // Field (Email Delivery - Provide Email)
    const priorityScore = getFieldById("customfield_10106"); // Field (Priority Score)
    const standardAddOns = getFieldById("customfield_10107"); // Field (Standard Add Ons)
    
    // Reset fields
    standardDataExtractType.setVisible(false).setRequired(false);
    
    switch(newOrEnhancementValue) {
        case "Standard - New":
            standardDataExtractType.setVisible(true).setRequired(true);
            areThereAnyTags.setVisible(true).setRequired(true);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false).setRequired(false);
            clientLevel.setRequired(false);
            businessRequirements.setRequired(false);
            isThisReusable.setVisible(false).setRequired(false);
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true).setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true).setRequired(true);
            modeOfDelivery.setVisible(true).setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(false);
            standardAddOns.setVisible(true);
            break;
            
        case "Standard - Enhancement":
            standardDataExtractType.setVisible(true).setRequired(true);
            areThereAnyTags.setVisible(true).setRequired(true);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false).setRequired(false);
            clientLevel.setRequired(false);
            businessRequirements.setRequired(false);
            isThisReusable.setVisible(false).setRequired(false);
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true).setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true).setRequired(true);
            modeOfDelivery.setVisible(true).setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(false);
            standardAddOns.setVisible(true);
            break;
            
        case "Custom - New":
            standardDataExtractType.setVisible(false).setRequired(false);
            areThereAnyTags.setVisible(false).setRequired(false);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false).setRequired(false);
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true).setRequired(true);
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true).setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true).setRequired(true);
            modeOfDelivery.setVisible(true).setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(true);
            standardAddOns.setVisible(false);
            break;
            
        case "Custom - Enhancement":
            standardDataExtractType.setVisible(false).setRequired(false);
            areThereAnyTags.setVisible(false).setRequired(false);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false).setRequired(false);
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true).setRequired(false);
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true).setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true).setRequired(true);
            modeOfDelivery.setVisible(true).setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(true);
            standardAddOns.setVisible(false);
            break;
            
        case "New":
            standardDataExtractType.setVisible(false).setRequired(false);
            areThereAnyTags.setVisible(false).setRequired(false);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false).setRequired(false);
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(false).setRequired(false);
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true).setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true).setRequired(true);
            modeOfDelivery.setVisible(true).setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            standardAddOns.setVisible(false);
            break;
            
        case "Enhancement":
            standardDataExtractType.setVisible(false).setRequired(false);
            areThereAnyTags.setVisible(false).setRequired(false);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false).setRequired(false);
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true).setRequired(false);
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true).setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true).setRequired(true);
            modeOfDelivery.setVisible(true).setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            standardAddOns.setVisible(false);
            break;
            
        default:
            standardAddOns.setVisible(false);
            break;
    }
    
    console.log("Behaviours CRUS Project – New or Enhancement completed.");
}
