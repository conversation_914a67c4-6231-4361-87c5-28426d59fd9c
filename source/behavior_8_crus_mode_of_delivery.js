/*
Creator: <PERSON>: CRUS project - Client Reporting - USGH - Hide fields based on "Mode of Delivery"
Change log: Rewritten for Jira Cloud compatibility
*/

// Check if "Mode of Delivery" field has changed
if (getChangeField().getName() === "Mode of Delivery") {
    console.log("Behaviours CRUS Project – Mode of Delivery started.");
    
    const modeOfDeliveryValue = getChangeField().getValue().value;
    console.log("Mode of Delivery:", modeOfDeliveryValue);
    
    // Get field references
    const vendorDetails = getFieldById("customfield_10132"); // Field (SFTP Host and Folder)
    const emailDelivery = getFieldById("customfield_10133"); // Field (Email Delivery - Provide Email)
    const boxLocation = getFieldById("customfield_10134"); // Field (Box Location)
    
    // Determine which fields to show based on selection
    const vendorDetailsNew = modeOfDeliveryValue === "SFTP";
    const emailDeliveryNew = modeOfDeliveryValue === "Email Delivery – For reports not containing PHI or PII";
    const boxLocationNew = modeOfDeliveryValue === "Box";
    
    // Show/Hide SFTP fields
    if (vendorDetailsNew) {
        vendorDetails.setVisible(true);
        vendorDetails.setRequired(true);
    } else {
        vendorDetails.setVisible(false);
        vendorDetails.setRequired(false);
    }
    
    // Show/Hide Email Delivery fields
    emailDelivery.setVisible(emailDeliveryNew);
    emailDelivery.setRequired(emailDeliveryNew);
    
    // Show/Hide Box Location fields
    boxLocation.setVisible(boxLocationNew);
    boxLocation.setRequired(boxLocationNew);
    
    console.log("Behaviours CRUS Project – Mode of Delivery completed.");
}
