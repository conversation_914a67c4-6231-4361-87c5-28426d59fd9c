/*
Creator: <PERSON>: CRUS project - Client Reporting - USGH - Hide fields based on "File Type Extension"
Change log: Rewritten for Jira Cloud compatibility
*/

// Check if "File Type Extension" field has changed
if (getChangeField().getName() === "File Type Extension") {
    console.log("Behaviours CRUS Project – File Type Extension started.");
    
    const fileTypeExtensionValue = getChangeField().getValue().value;
    console.log("File Type Extension:", fileTypeExtensionValue);
    
    // Get the Extension Details field reference
    const extensionDetails = getFieldById("customfield_10131"); // Field (Extension Details)
    
    // Show/Hide Extension Details field based on selection
    const showExtensionDetails = fileTypeExtensionValue === "other";
    
    extensionDetails.setVisible(showExtensionDetails);
    extensionDetails.setRequired(showExtensionDetails);
    
    console.log("Behaviours CRUS Project – File Type Extension completed.");
}
