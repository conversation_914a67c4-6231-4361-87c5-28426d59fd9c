/*
Creator: <PERSON>pose: CRUS project - Client Reporting - USGH - Hide fields based on Standard Data Extract Type
Change log: Rewritten for Jira Cloud compatibility
*/

// Check if Standard Data Extract Type field has changed
if (getChangeField().getName() === "Standard Data Extract Type") {
    console.log("Behaviours CRUS Project – Standard Data Extract Type started.");
    
    const standardReportTypeOption = getChangeField().getValue().value;
    console.log("Standard Data Extract Type:", standardReportTypeOption);
    
    // Get field references with proper custom field IDs
    const membershipReport = getFieldById("customfield_10110"); // Field (Membership Report)
    const bloodGlucoseReport = getFieldById("customfield_10111"); // Field (Blood Glucose Report)
    const bloodPressureReport = getFieldById("customfield_10112"); // Field (Blood Pressure Report)
    const weightManagementReport = getFieldById("customfield_10113"); // Field (Weight Management Report)
    const alertsReport = getFieldById("customfield_10114"); // Field (Alerts Report)
    const coachingReport = getFieldById("customfield_10115"); // Field (Coaching Report)
    const grievanceReport = getFieldById("customfield_10116"); // Field (Grievance Report)
    const incentiveReport = getFieldById("customfield_10117"); // Field (Incentive Report)
    const meterMessageReport = getFieldById("customfield_10118"); // Field (Meter Message Report)
    const engagementReport = getFieldById("customfield_10119"); // Field (Engagement Report)
    const ineligibleMembersReport = getFieldById("customfield_10120"); // Field (Ineligible Members Report)
    const mentalHealthEventsReport = getFieldById("customfield_10121"); // Field (Mental Health Events Report)
    const marketingOptOutReport = getFieldById("customfield_10122"); // Field (Marketing Opt Out Report)
    const contractLanguageForGrievanceReporting = getFieldById("customfield_10123"); // Field (Contract Language for Grievance Reporting)
    const eMSCaseDetailExtractReport = getFieldById("customfield_10124"); // Field (EMS Case Detail Extract Report)
    const estimatedA1cReport = getFieldById("customfield_10125"); // Field (Estimated A1c Report)
    const clinicalOutcomesMemberReport = getFieldById("customfield_10126"); // Field (Clinical Outcomes Member Report)
    const detailedInvoicesReport = getFieldById("customfield_10127"); // Field (Detailed Invoices Report)
    const registrationReport = getFieldById("customfield_10128"); // Field (Registration Report)
    
    // Determine which reports to show based on selection
    const mentalHealthEventsReportNew = standardReportTypeOption === "Mental Health Events";
    const bloodGlucoseReportNew = standardReportTypeOption === "Blood Glucose";
    const alertsReportNew = standardReportTypeOption === "Blood Glucose Alerts";
    const bloodPressureReportNew = standardReportTypeOption === "Blood Pressure";
    const coachingReportNew = standardReportTypeOption === "Coaching";
    const engagementReportNew = standardReportTypeOption === "Engagement";
    const grievanceReportNew = standardReportTypeOption === "Grievances";
    const incentiveReportNew = standardReportTypeOption === "Incentive Report";
    const ineligibleMembersReportNew = standardReportTypeOption === "Ineligible Members";
    const marketingOptOutReportNew = standardReportTypeOption === "Marketing Opt Out";
    const membershipReportNew = standardReportTypeOption === "Membership";
    const meterMessageReportNew = standardReportTypeOption === "Meter Message";
    const weightManagementReportNew = standardReportTypeOption === "Weight Management";
    const eMSCaseDetailExtractReportNew = standardReportTypeOption === "EMS Case Detail Extract";
    const estimatedA1cReportNew = standardReportTypeOption === "Estimated A1c (Health Plans Only)";
    const clinicalOutcomesMemberReportNew = standardReportTypeOption === "Quarterly Clinical Outcomes Member File";
    const detailedBillingReportNew = standardReportTypeOption === "Detailed Invoices";
    const registrationReportNew = standardReportTypeOption === "Registration";
    
    // Show/Hide fields based on selection
    membershipReport.setVisible(membershipReportNew);
    bloodGlucoseReport.setVisible(bloodGlucoseReportNew);
    bloodPressureReport.setVisible(bloodPressureReportNew);
    weightManagementReport.setVisible(weightManagementReportNew);
    alertsReport.setVisible(alertsReportNew);
    coachingReport.setVisible(coachingReportNew);
    grievanceReport.setVisible(grievanceReportNew);
    incentiveReport.setVisible(incentiveReportNew);
    meterMessageReport.setVisible(meterMessageReportNew);
    engagementReport.setVisible(engagementReportNew);
    ineligibleMembersReport.setVisible(ineligibleMembersReportNew);
    mentalHealthEventsReport.setVisible(mentalHealthEventsReportNew);
    marketingOptOutReport.setVisible(marketingOptOutReportNew);
    contractLanguageForGrievanceReporting.setVisible(grievanceReportNew);
    eMSCaseDetailExtractReport.setVisible(eMSCaseDetailExtractReportNew);
    estimatedA1cReport.setVisible(estimatedA1cReportNew);
    clinicalOutcomesMemberReport.setVisible(clinicalOutcomesMemberReportNew);
    detailedInvoicesReport.setVisible(detailedBillingReportNew);
    registrationReport.setVisible(registrationReportNew);
    
    console.log("Behaviours CRUS Project – Standard Data Extract Type completed.");
}
