/*
Creator: <PERSON>pose: If an issue's parent Epic has the "Build/Run" field set to "Supporting", then make the 5 Run Allocation fields visible and required
Change log: Rewritten for Jira Cloud compatibility
*/

// Access the context for the UI Modification
const context = await getContext();

console.log("Behaviour Script 'Run Allocation Field Logic' has started.");

// Utility: Fetch issue by key
async function getIssueByKey(ikey) {
    const res = await makeRequest("/rest/api/2/issue/" + ikey);
    console.log(res.body.key, res.body.fields);
    return res.body;
}

// Check if Parent field has changed
if (getChangeField().getName() === "Parent") {
    const changedField = getChangeField();
    
    if (changedField.getValue() && changedField.getValue().key) {
        const parentValue = changedField.getValue().key;
        const parentIssue = await getIssueByKey(parentValue);
        const buildRunValue = parentIssue.fields.customfield_10093?.value; // Field (Build/Run)
        
        console.log("Parent Issue Build/Run value:", buildRunValue);
        
        if (buildRunValue && buildRunValue !== "Business") {
            // Show the Run Allocation fields and set them as required
            getFieldById("customfield_10126").setVisible(true).setRequired(true); // Field (Eng - Department)
            getFieldById("customfield_10067").setVisible(true).setRequired(true); // Field (Eng - Platform)
            getFieldById("customfield_10086").setVisible(true).setRequired(true); // Field (RUN Type)
            getFieldById("customfield_10096").setVisible(true).setRequired(true); // Field (Deployed Location)
            getFieldById("customfield_10068").setVisible(true).setRequired(true); // Field (Deployed Product)
        } else {
            // Hide the Run Allocation fields and make them optional
            getFieldById("customfield_10126").setVisible(false).setRequired(false); // Field (Eng - Department)
            getFieldById("customfield_10067").setVisible(false).setRequired(false); // Field (Eng - Platform)
            getFieldById("customfield_10086").setVisible(false).setRequired(false); // Field (RUN Type)
            getFieldById("customfield_10096").setVisible(false).setRequired(false); // Field (Deployed Location)
            getFieldById("customfield_10068").setVisible(false).setRequired(false); // Field (Deployed Product)
        }
    } else {
        // If no parent is set, hide the Run Allocation fields
        getFieldById("customfield_10126").setVisible(false).setRequired(false); // Field (Eng - Department)
        getFieldById("customfield_10067").setVisible(false).setRequired(false); // Field (Eng - Platform)
        getFieldById("customfield_10086").setVisible(false).setRequired(false); // Field (RUN Type)
        getFieldById("customfield_10096").setVisible(false).setRequired(false); // Field (Deployed Location)
        getFieldById("customfield_10068").setVisible(false).setRequired(false); // Field (Deployed Product)
    }
}

console.log("Behaviour Script 'Run Allocation Field Logic' has completed.");
