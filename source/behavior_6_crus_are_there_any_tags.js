/*
Creator: <PERSON>: CRUS project - Client Reporting - USGH - Hide fields based on "Are there any tags?"
Change log: Rewritten for Jira Cloud compatibility
*/

// Check if "Are there any tags?" field has changed
if (getChangeField().getName() === "Are there any tags?" && getChangeField().getValue().value === "Yes") {
    console.log("Behaviours CRUS Project – Are there any tags? started.");
    
    // Get the Tags field reference
    const tags = getFieldById("customfield_10130"); // Field (Tags)
    
    // Show the Tags field, make it required, and set help text
    tags.setDescription("Please provide tag, unique identifier, client specific identifier (separate each tag by comma).");
    tags.setRequired(true);
    tags.setVisible(true);
    
    console.log("Behaviours CRUS Project – Are there any tags? completed.");
}
