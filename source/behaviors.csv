INDEX,script_to,Final Script
1,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: <PERSON>
Purpose: RC-Platform field -> Populate RC-Platform Owner
Change log: ER 10-11-2024 JIRA-10050
            ER 11-112024 JIRA-10169
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours
final projectName = issueContext?.projectObject?.name
final issueTypeName = issueContext?.getIssueType()?.getName()
final loggedInUser = ComponentAccessor?.jiraAuthenticationContext?.loggedInUser
final rcPlatformOwner = underlyingIssue?.getCustomFieldValue(""RC-Platform Owner"")
final origRCPlatform = underlyingIssue?.getCustomFieldValue(""RC-Platform"")
let origRCPlatformValue = origRCPlatform?.getValue()  //.getValue()s() as HashMap
let rcPlatform = getFieldById(getFieldChanged())
let rcPlatformValue = rcPlatform?.getValue()
log.debug(""rcPlatformValue: ${rcPlatformValue}"")
log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")
if (getAction()?.id == null || !(getAction()?.id == 1 )){
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field **EDIT & VIEW Screens**."")
    //If someone changes Platform, we need to update the Platform Owner also
    if(origRCPlatformValue?.toString() != rcPlatformValue?.toString()) {
        populateChangeOwner()
    }
}else{ //This is the initial Create Action
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field  **Create Action**."")
    populateChangeOwner()
}
log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions
let populateChangeOwner() {
    //let platform = underlyingIssue?.getCustomFieldValue(""Platform"")
    let rcPlatform = getFieldById(""RC-Platform"")
    let rcPlatformValue = rcPlatform?.getValue() as String
    log.debug(""rcPlatformValue: ${rcPlatformValue}"")
    if(rcPlatformValue == null || rcPlatformValue == """"){return ""RC Platform is empty.""}
    let rcPlatformOwner = getFieldById(""RC-Platform Owner"")
    switch(rcPlatformValue?.toLowerCase()){
        case ""clinical usgh"": // updated ""clinical"" to ""clinical usgh"" JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""consumer experience"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""corporate applications"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""cybersecurity"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""data"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""devops engineering"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""ecosystem"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;        
        case ""engagement"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs engineering"": // added JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs platform ops"": // added JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs technical operations"": // added JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""marketing operations"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""member support"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""product management"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;        
        case ""service, benefit, and client"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""solutions delivery"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""supply chain"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;        
        case ""tdh international"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""technology services"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""technical operations"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""workplace services"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        //Adding the new value JIRA-10169
        case ""hhs solution analysis and design"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        default:
            rcPlatformOwner?.setValue("""")
            rcPlatformOwner?.setReadOnly(false)
        break;

    }
}",
2,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: CA-Platform field -> Populate CA-Platform Owner
Change log: ER 10-11-2024 JIRA-10050
            ER 11-112024 JIRA-10169
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours
final projectName = issueContext?.projectObject.name
final issueTypeName = issueContext?.getIssueType()?.getName()
final loggedInUser = ComponentAccessor?.jiraAuthenticationContext?.loggedInUser
final caPlatformOwner = underlyingIssue?.getCustomFieldValue(""CA-Platform Owner"")
final origCAPlatform = underlyingIssue?.getCustomFieldValue(""CA-Platform"")
let origCAPlatformValue = origCAPlatform?.getValue()  //.getValue()s() as HashMap
let caPlatform = getFieldById(getFieldChanged())
let caPlatformValue = caPlatform?.getValue()

log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")
if (getAction()?.id == null || !(getAction()?.id == 1 )){
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field **EDIT & VIEW Screens**."")
    //If someone changes CA-Platform, we need to update the CA-Platform Owner also
    if(origCAPlatformValue?.toString() != caPlatformValue?.toString()) {
        populateChangeOwner()
    }
}else{ //This is the initial Create Action
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field  **Create Action**."")
    populateChangeOwner()
}
log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions
let populateChangeOwner() {
    //let platform = underlyingIssue?.getCustomFieldValue(""Platform"")
    let caPlatform = getFieldById(""CA-Platform"")
    let caPlatformValue = caPlatform?.getValue() as String
    if(caPlatformValue == null || caPlatformValue == """"){return ""RC Platform is empty.""}
    let caPlatformOwner = getFieldById(""CA-Platform Owner"")
    switch(caPlatformValue?.toLowerCase()){
        case ""clinical usgh"": // updated ""clinical"" to ""clinical usgh"" JIRA-10050
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""consumer experience"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""corporate applications"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""cybersecurity"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""data"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""devops engineering"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""ecosystem"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""engagement"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs engineering"": // added JIRA-10050
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs platform ops"": // added JIRA-10050
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs technical operations"": // added JIRA-10050
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""marketing operations"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""member support"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""product management"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""service, benefit, and client"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""solutions delivery"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""supply chain"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""tdh international"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""technical operations"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""technology services"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""workplace services"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        //Adding the new value JIRA-10169
        case ""hhs solution analysis and design"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        default:
            caPlatformOwner?.setValue("""")
            caPlatformOwner?.setReadOnly(false)
        break;
    }
}",
3,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: Impacted Countries = Other display 'Other' field
Change log:
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours

let impactedCountries = getFieldById(getFieldChanged())
String impactedCountriesValue = impactedCountries?.getValue().toString().replaceAll(""\\["", """").replaceAll(""\\]"","""")
let other = getFieldById(""Other"")
other?.setVisible(false).setRequired(false)
log.debug(""impactedCountriesValue: $impactedCountriesValue}"")

if(impactedCountriesValue.includes('Other')){  //} == 'Other'){
    log.debug(""impactedCountriesValue-1: $impactedCountriesValue}"")
    other?.setVisible(true).setRequired(true)
    other?.setDescription(""For other countries not available in the dropdown."")
}",
4,,
5,"// Had Imports
// com.atlassian.jira.user.ApplicationUser
// com.atlassian.jira.issue.IssueInputParameters
// com.atlassian.jira.issue.priority.Priority
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.event.type.EventDispatchOption
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// com.atlassian.jira.security.roles.ProjectRoleManager
// groovy.transform.BaseScript
// org.apache.log4j.Logger
// org.apache.log4j.Level
//Creator: Jeff Melies
//Purpose: Set Priority depending on selection of both Impact & Urgency
//Change log:

log.setLevel(Level.DEBUG)

Issue issue = underlyingIssue

@BaseScript FieldBehaviours fieldBehaviours
//Managers
// Rewritten / ProjectRoleManager Substitution
let context = await getContext()
let projectKey = context.extension.project.key
let resMyRolesInProject = await makeRequest(`/rest/api/2/project/${projectKey}/roledetails?currentMember=True`)
let myRolesInProject = resMyRolesInProject.body            
              
let customFieldManager = ComponentAccessor.getCustomFieldManager()
let optionsManager = ComponentAccessor.getOptionsManager()

final projectName = issueContext.projectObject.name
log = Logger.getLogger(""${issue}: Behaviours: ${projectName}-Field: Impact"") 

final issueTypeName = issueContext.getIssueType().getName()
let impact = getFieldById(getFieldChanged()) //Impact
String origImpact = issue?.getCustomFieldValue(""Impact"").toString()  //Original Impact value
String urgencyValue = issue?.getCustomFieldValue(""Urgency"").toString()
//getFieldById(""Priority"").setReadOnly(true)

if (!(origImpact || urgencyValue)){
    log.debug(""'Impact' or 'Urgency' field is empty EXIT EARLY"")
    return
}

log.debug(""Has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

if (getAction()?.id == null || !(getAction()?.id == 1 )){
    log.debug(""**EDIT & VIEW Screens**"")
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
    //If someone changes the Impact field do the following
//    fieldSetup()               *******************ENABLE THIS TO GET THE AUTOMATION TO WORK******
}else{ //This is the initial Create Action
    log.debug(""**Create action**"")
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
//    fieldSetup()               *******************ENABLE THIS TO GET THE AUTOMATION TO WORK******
}
log.debug(""Has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions
let fieldSetup(){
    let issueService = ComponentAccessor.issueService
    Issue issue = underlyingIssue
    String impactValue = getFieldById(""Impact"")?.getValue().toString().toLowerCase()
    String urgencyValue = getFieldById(""urgency"")?.getValue().toString().toLowerCase()
    let availablePriorities = ComponentAccessor.constantsManager.priorities
    Priority newPriority
    let pr
    switch(impactValue){
        case ""high (extensive/widespread)"":
            if(urgencyValue == ""high (immediately)""){
                newPriority = availablePriorities.find { it.name == ""P1"" }
            }else if(urgencyValue == ""medium (hours)""){
                newPriority = availablePriorities.find { it.name == ""P2"" }
            }else{
                newPriority = availablePriorities.find { it.name == ""P3"" }
            }
        break;
        case ""medium (significant/large)"":
            if(urgencyValue == ""high (immediately)""){
                newPriority = availablePriorities.find { it.name == ""P2"" }
            }else if(urgencyValue == ""medium (hours)""){
                newPriority = availablePriorities.find { it.name == ""P3"" }
            }else{
                newPriority = availablePriorities.find { it.name == ""P4"" }
            }
        break;
        case ""low (minor)"":
            if(urgencyValue == ""high (immediately)""){
                newPriority = availablePriorities.find { it.name == ""P3"" }
            }else if(urgencyValue == ""medium (hours)""){
                newPriority = availablePriorities.find { it.name == ""P4"" }
            }else{
                newPriority = availablePriorities.find { it.name == ""P5"" }
            }
        break;
        default:
            getFieldById(""Priority"").setVisible(true).setReadOnly(true)
            newPriority = null
        break;
    }
    assert newPriority : ""Could not find priority name. Available priorities are ${availablePriorities*.name.join("", "")}""
    let issueInputParams = issueService.newIssueInputParameters() as IssueInputParameters
    issueInputParams.with {
        priorityId = newPriority.id
    }

    let updateValidationResult = issueService.validateUpdate(ComponentAccessor.jiraAuthenticationContext.loggedInUser, issue?.id, issueInputParams)
    assert updateValidationResult.valid : updateValidationResult.errorCollection

    let updateResult = issueService.update(ComponentAccessor.jiraAuthenticationContext.loggedInUser, updateValidationResult, EventDispatchOption.DO_NOT_DISPATCH, false)
    assert updateResult.valid : updateResult.errorCollection

}",
6,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: Platform field -> Populate Platform Owner
Change log:
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours
final projectName = issueContext.projectObject.name
final issueTypeName = issueContext.getIssueType().getName()
final loggedInUser = ComponentAccessor.jiraAuthenticationContext.loggedInUser
final platformOwner = underlyingIssue?.getCustomFieldValue(""Platform Owner"")
final origPlatform = underlyingIssue?.getCustomFieldValue(""Platform"")
let origPlatformValue = origPlatform?.getValue()  //.getValue()s() as HashMap
let platform = getFieldById(getFieldChanged())
let platformValue = platform?.getValue()

log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Platform' Field has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")
if (getAction()?.id == null || !(getAction()?.id == 1 )){
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Platform' Field **EDIT & VIEW Screens**."")
    //If someone changes CA-Platform, we need to update the Platform Owner also
    if(origPlatformValue.toString() != platformValue.toString()) {
        populateChangeOwner()
    }
}else{ //This is the initial Create Action
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Platform' Field  **Create Action**."")
    populateChangeOwner()
}
log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Platform' Field has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions
let populateChangeOwner() {
    //let platform = underlyingIssue?.getCustomFieldValue(""Platform"")
    let platform = getFieldById(""Platform"")
    let platformValue = platform.getValue() as String
    let platformOwner = getFieldById(""Platform Owner"")
    switch(platformValue.toLowerCase()){
        case ""clinical"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""consumer"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""corporate applications"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""cybersecurity"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""data"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""devops"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""marketing"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""member support"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""service & client"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""solutions delivery"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""tdh international"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""technology services"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""technical operations (coe)"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""workplace services"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        default:
            platformOwner.setValue("""")
            platformOwner.setReadOnly(false)
        break;

    }
}",
7,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: RC-Platform field -> Populate RC-Platform Owner
Change log:
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours
final projectName = issueContext.projectObject.name
final issueTypeName = issueContext.getIssueType()?.getName()
final loggedInUser = ComponentAccessor.jiraAuthenticationContext.loggedInUser
final rcPlatformOwner = underlyingIssue?.getCustomFieldValue(""RC-Platform Owner"")
final origRCPlatform = underlyingIssue?.getCustomFieldValue(""RC-Platform"")
let origRCPlatformValue = origRCPlatform?.getValue()  //.getValue()s() as HashMap
let rcPlatform = getFieldById(getFieldChanged())
let rcPlatformValue = rcPlatform?.getValue()

log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")
if (getAction()?.id == null || !(getAction()?.id == 1 )){
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field **EDIT & VIEW Screens**."")
    //If someone changes Platform, we need to update the Platform Owner also
    if(origRCPlatformValue.toString() != rcPlatformValue.toString()) {
        populateChangeOwner()
    }
}else{ //This is the initial Create Action
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field  **Create Action**."")
    populateChangeOwner()
}
log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions
let populateChangeOwner() {
    //let platform = underlyingIssue?.getCustomFieldValue(""Platform"")
    let rcPlatform = getFieldById(""RC-Platform"")
    let rcPlatformValue = rcPlatform.getValue() as String
    let rcPlatformOwner = getFieldById(""RC-Platform Owner"")
    switch(rcPlatformValue?.toLowerCase()){
       case ""clinical usgh"": // updated ""clinical"" to ""clinical usgh"" JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""consumer experience"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""corporate applications"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""cybersecurity"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""data"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""devops engineering"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""ecosystem"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;        
        case ""engagement"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs engineering"": // added JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs platform ops"": // added JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs technical operations"": // added JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""marketing operations"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""member support"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""product management"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;        
        case ""service, benefit, and client"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""solutions delivery"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""supply chain"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;        
        case ""tdh international"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""technology services"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""technical operations"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""workplace services"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        //Adding the new value JIRA-10169
        case ""hhs solution analysis and design"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        default:
            rcPlatformOwner?.setValue("""")
            rcPlatformOwner?.setReadOnly(false)
        break;
    }
}",
8,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: CA-Platform field -> Populate CA-Platform Owner
Change log:
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours
final projectName = issueContext.projectObject.name
final issueTypeName = issueContext.getIssueType()?.getName()
final loggedInUser = ComponentAccessor.jiraAuthenticationContext.loggedInUser
final caPlatformOwner = underlyingIssue?.getCustomFieldValue(""CA-Platform Owner"")
final origCAPlatform = underlyingIssue?.getCustomFieldValue(""CA-Platform"")
let origCAPlatformValue = origCAPlatform?.getValue()  //.getValue()s() as HashMap
let caPlatform = getFieldById(getFieldChanged())
let caPlatformValue = caPlatform?.getValue()

log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")
if (getAction()?.id == null || !(getAction()?.id == 1 )){
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field **EDIT & VIEW Screens**."")
    //If someone changes CA-Platform, we need to update the CA-Platform Owner also
    if(origCAPlatformValue.toString() != caPlatformValue.toString()) {
        populateChangeOwner()
    }
}else{ //This is the initial Create Action
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field  **Create Action**."")
    populateChangeOwner()
}
log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions
let populateChangeOwner() {
    //let platform = underlyingIssue?.getCustomFieldValue(""Platform"")
    let caPlatform = getFieldById(""CA-Platform"")
    let caPlatformValue = caPlatform?.getValue() as String
    let caPlatformOwner = getFieldById(""CA-Platform Owner"")
    switch(caPlatformValue?.toLowerCase()){
        case ""clinical"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""consumer experience"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""corporate applications"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""cybersecurity"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""data"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""devops engineering"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""ecosystem"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""engagement"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""marketing operations"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""member support"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""product management"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""service, benefit, and client"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""solutions delivery"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""supply chain"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""tdh international"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""technical operations"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""technology services"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""workplace services"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        default:
            caPlatformOwner?.setValue("""")
            caPlatformOwner?.setReadOnly(false)
        break;
    }
}",
9,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: Impacted Countries = Other display 'Other' field
Change log:
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours

let impactedCountries = getFieldById(getFieldChanged())
String impactedCountriesValue = impactedCountries?.getValue().toString()
let other = getFieldById(""Other"")
other.setVisible(false).setRequired(false)

if(impactedCountriesValue.includes('Other')){  //} == 'Other'){ == 'Other'){
    other.setVisible(true).setRequired(true)
}",
10,"
// Checks if the ""Category"" field has been changed
let changedFieldValue = getFieldById(getFieldChanged()).getValue()
let description = getFieldById(""Description"")

console.log(""Category changed to: '$changedFieldValue'"")

let actionName = getActionName()
console.log(""Action was: '$actionName'"")

if (getActionName() in [""Create Issue"",""Create""]) {

    if (changedFieldValue == ""New SSO configuration request"") {

        let strDescription = """"""
        *Customer Name:*

        *Org ID* (if applicable):

        *Group ID* (if applicable):

        *Eligibility Type* (Example: Eligibility File or RTE or Hybrid):

        *Payer:*

        *Contact Information - Please provide the best point of contact for the following:*
        * Coordinating the implementation and testing phases
        * Troubleshooting any production issues after implementation
        * Updating certificates

        *Timeline/Expected Go Live Date* (NOTE: New SSO requests take 4-6 weeks. It can be faster based on information gathering):

        *Additional Details:*

        *NOTE:*  _(These links only become clickable once the ticket has been created)_ Please provide [Technical Specifications Overview.docx|https://teladocpa.sharepoint.com/:w:/r/sites/Solution/Shared%20Documents/General/Integrations/USGH/SSO/Client%20Documents/Technical%20Specifications%20Overview.docx?d=wa0b6a116745c40af91423483614e46f7&csf=1&web=1&e=SoGAad] and [Teladoc Single Sign On Business Requirement Document Template.docx|https://teladocpa.sharepoint.com/:w:/r/sites/Solution/Shared%20Documents/General/Integrations/USGH/SSO/Client%20Documents/Teladoc%20Single%20Sign%20On%20Business%20Requirement%20Document%20Template.docx?d=w724f66f9b8954e6f9ee057160bc1f75a&csf=1&web=1&e=0iRzaQ] to new SSO client. 
        * If you cannot open above files, move your mouse cursor to [Technical Specifications Overview.docx|https://teladocpa.sharepoint.com/:w:/r/sites/Solution/Shared%20Documents/General/Integrations/USGH/SSO/Client%20Documents/Technical%20Specifications%20Overview.docx?d=wa0b6a116745c40af91423483614e46f7&csf=1&web=1&e=SoGAad] or [Teladoc Single Sign On Business Requirement Document Template.docx|https://teladocpa.sharepoint.com/:w:/r/sites/Solution/Shared%20Documents/General/Integrations/USGH/SSO/Client%20Documents/Teladoc%20Single%20Sign%20On%20Business%20Requirement%20Document%20Template.docx?d=w724f66f9b8954e6f9ee057160bc1f75a&csf=1&web=1&e=0iRzaQ]. Click right mouse cursor and click open in new tab. You will be able to see those documents. Please get back to this ticket when you get completed Teladoc Single Sign On Business Requirement Document word file from a client.
        """"""

        description.setValue(strDescription)
    } else if (changedFieldValue == ""Existing SSO configuration (Adding or removing groups) request"") {

        let strDescription = """"""
        *Customer Name:*  

        *Issuer/Entity ID:*  

        *Adding or Removing SSO:*  

        *Org ID* (if applicable):  

        *Group ID* (if applicable): 

        *Eligibility Type* (Example: Eligibility File or RTE or Hybrid):

        *Contact Information - Please provide the best point of contact for the following:*
        * (Adding groups only) *testing phases and troubleshooting any production issues*

        *Expected Due Date:*  

        *Additional Details:*
        """"""

        description.setValue(strDescription)
    } else if (changedFieldValue == ""SSO troubleshooting request"") {

        let strDescription = """"""
        *Customer Name:*  

        *Issuer/Entity ID:*  

        *Org ID* (if applicable):  

        *Group ID* (if applicable): 

        *Eligibility Type* (Example: Eligibility File or RTE or Hybrid):

        *Please provide detailed issue description* (It is helpful to troubleshoot faster):  

        *Issue Member information* (Please provide *First name, Last name, Date of Birth*, and *Health Plan ID*):

        *NOTE: Please get SAML SSO response log from the client. If you get SAML SSO response logs from clients and attach in this Jira ticket, it is faster to troubleshoot.*
        """"""

        description.setValue(strDescription)
    } else if (changedFieldValue == ""SSO certificate (x509 certificate) renewal request"") {

        let strDescription = """"""
        *Customer Name:*

        *Issuer/Entity ID:*

        *Expiration Date of Existing Cert:*

        *Attach x509 certificate file* (.txt file):

        *Contact Information - Please provide the best point of contact for the following:*
        * Any issues with the X509 cert(s)

        *Additional Details:*

        *Note:* Certificate renewals generally do not require a coordinated call as the Teladoc system can handle multiple certs. However, if the customer prefers to arrange a time for cert update, please indicate that in additional details.
        """"""

        description.setValue(strDescription)
    } else if (changedFieldValue == ""Adding test member in UAT environment request"") {

        let strDescription = """"""
        *Customer Name:*  

        *Issuer/Entity ID:*  

        *Org ID (if applicable):*  

        *Group ID (if applicable):*  

        *Eligibility Type* (Example: Eligibility File or RTE or Hybrid):  

        *Test Member information* (Please provide *First name, Last name, Date of Birth*, and *Health Plan ID*):

        *Expected Due Date:*  

        *Additional Details:*  
        """"""

        description.setValue(strDescription)
    }

}",
11,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * PMA project
 * Update ""Request Type"" available options based on ""Issue Type""
 */
console.log(""Behaviours PMA Project – Issue Type started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();


// This listens if the ""Issue Type"" field has changed
let issueType = getFieldById(getFieldChanged());

let requestType = getFieldById(""Request Type"");

let customField = customFieldManager.getCustomFieldObject(requestType.getFieldId())
let config = customField.getRelevantConfig(getIssueContext())
let options = optionsManager.getOptions(config)
console.log(""issueContext.issueType.name:""+ issueContext.issueType.name)

switch(issueContext.issueType.name){
    case ""Service Request with Approvals"":
        //requestType.setOptionsVisibility(options.filter(it.getValue() in ['New Claims Suppression', 'Extension of an Existing Claims Suppression','Pre Existing Suppression (GM Consults Included)','Price Overrides']})
        requestType.setOptionsVisibility(options.filter(it.getValue() in ['Price Overrides']})
        requestType.setRequired(true);
        requestType.setValue('Price Overrides')
    break;
    case ""Task"":
        requestType.setOptionsVisibility(options.filter(it.getValue() in ['Core','CCM', 'Both']})
        requestType.setRequired(true);
    break;
    default:
        requestType.setRequired(false);
    break;
}",
12,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * PMA project
 * Update ""Request Type"" available options based on ""Issue Type""
 */
console.log(""Behaviours PMA Project – Request Type started."");


let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

let requestType = getFieldById(getFieldChanged());

// This gets the value of ""Request Type"" as String
let requestTypeOption = requestType.getValue() as String

// Custom fields for all Request Type options except for ""Price Overrides"" option
let originalDueDate = getFieldById(""Original due date"");
let dateOccurred = getFieldById(""Date Occurred"");
let endDate = getFieldById(""End Date"");
let dueDate = getFieldById(""Due Date""); // added JIRA-10090
let typeSL = getFieldById(""Type (SL)"");
let isThisaGMConsultsIncludedRequest = getFieldById(""Is this a GM Consults included request?"");
let clientName = getFieldById(""Client Name"");
let doGroupNeedToBeSupressed = getFieldById(""Do group(s) need to be supressed?"");
let areThereAnyProductsThatNeedToBeSuppressed = getFieldById(""Are there any products that need to be suppressed?"");
let productName = getFieldById(""Product Name"");

//Custom fields for ""Price Overrides"" Request Type option
//Description 
let actualEndDate = getFieldById(""Actual End Date"");
let groupPayerCount = getFieldById(""Group/Payer Count"");
let description = getFieldById(""Description"");

let strDescription = """"""
|*Question*|*Answer*|*Question*|*Answer*|
|*Health Plan:*| |*Legacy ID/Group ID:*
{color:#de350b}**If multiple please attach spreadsheet*{color}| |
|*TEIT Ticket #:*
{color:#de350b}**Analysis required before submitting*{color}| |*New, Modification, Removal:*| |
|*Effective Date:*| |*Expiration Date:*| |
|*Pricing (Dollar Amount):*
{color:#de350b}**If multiple please attach spreadsheet*{color}| |*Products:* 
{color:#de350b}**(GM, MH, Derm, VPC, Nutrition)*{color}| |
"""""" 

description.setRequired(true);
productName.setVisible(false);
dateOccurred.setHelpText(""Effective Date of Suppression."");

switch(requestTypeOption){
    case ""New Claims Suppression"":
        actualEndDate.setVisible(false);
        groupPayerCount.setVisible(false);
        //Only run on the create screen
        if ((getActionName() == ""Create"")) {
            description.setValue("""");
        }
        originalDueDate.setVisible(false);
        originalDueDate.setRequired(false);
        dateOccurred.setVisible(true);
        dateOccurred.setRequired(true);
        endDate.setVisible(true);
        endDate.setRequired(true);
        dueDate.setVisible(false); // added JIRA-10090
        
        typeSL.setVisible(true);
        typeSL.setRequired(true);
        //isThisaGMConsultsIncludedRequest.setVisible(true);
        //isThisaGMConsultsIncludedRequest.setRequired(true);
        clientName.setVisible(true);
        clientName.setRequired(true);
        doGroupNeedToBeSupressed.setVisible(true);
        //doGroupNeedToBeSupressed.setRequired(true);
        areThereAnyProductsThatNeedToBeSuppressed.setVisible(true);
        //areThereAnyProductsThatNeedToBeSuppressed.setRequired(true)
    break;
    case ""Extension of an Existing Claims Suppression"":
        actualEndDate.setVisible(false);
        groupPayerCount.setVisible(false);
        //Only run on the create screen
        if ((getActionName() == ""Create"")) {
            description.setValue("""");
        }
        originalDueDate.setVisible(true);
        originalDueDate.setRequired(true);
        dateOccurred.setVisible(false);
        dateOccurred.setRequired(false);
        endDate.setVisible(false);
        endDate.setRequired(false);
        dueDate.setVisible(false); // added JIRA-10090

        typeSL.setVisible(true);
        typeSL.setRequired(true);
        //isThisaGMConsultsIncludedRequest.setVisible(true);
        clientName.setVisible(true);
        clientName.setRequired(true);
        doGroupNeedToBeSupressed.setVisible(true);
        areThereAnyProductsThatNeedToBeSuppressed.setVisible(true);
    break;
    case ""Pre Existing Suppression (GM Consults Included)"":
        actualEndDate.setVisible(false);
        groupPayerCount.setVisible(false);
        //Only run on the create screen
        if ((getActionName() == ""Create"")) {
            description.setValue("""");
        }
        originalDueDate.setVisible(false);
        originalDueDate.setRequired(false);
        dateOccurred.setVisible(true);
        dateOccurred.setRequired(true);
        endDate.setVisible(true)
        endDate.setRequired(true);
        dueDate.setVisible(false); // added JIRA-10090

        typeSL.setVisible(true);
        typeSL.setRequired(true);
        //isThisaGMConsultsIncludedRequest.setVisible(true);
        clientName.setVisible(true);
        clientName.setRequired(true);
        doGroupNeedToBeSupressed.setVisible(true);
        areThereAnyProductsThatNeedToBeSuppressed.setVisible(true);
    break;
    case ""Price Overrides"":
        actualEndDate.setVisible(true);
        groupPayerCount.setVisible(true);
        //Only run on the create screen
        if ((getActionName() == ""Create"")) {
            description.setValue(strDescription);
        }
        originalDueDate.setVisible(false);
        originalDueDate.setRequired(false);
        dateOccurred.setVisible(false);
        dateOccurred.setRequired(false);
        endDate.setVisible(false);
        endDate.setRequired(false);
        dueDate.setVisible(true); // added JIRA-10090

        typeSL.setVisible(false);
        typeSL.setRequired(false);
        //isThisaGMConsultsIncludedRequest.setVisible(false);
        clientName.setVisible(false);
        clientName.setRequired(false);
        doGroupNeedToBeSupressed.setVisible(false);
        areThereAnyProductsThatNeedToBeSuppressed.setVisible(false);
    break;
    default:
        actualEndDate.setVisible(true);
        groupPayerCount.setVisible(true);
        //Only run on the create screen
        if ((getActionName() == ""Create"")) {
            description.setValue("""");
        }
        originalDueDate.setVisible(false);
        dateOccurred.setVisible(false);
        endDate.setVisible(true)

        typeSL.setVisible(false);
        isThisaGMConsultsIncludedRequest.setVisible(false);
        clientName.setVisible(false);
        clientName.setRequired(false);
        doGroupNeedToBeSupressed.setVisible(false);
        areThereAnyProductsThatNeedToBeSuppressed.setVisible(false);
    break;
}",
13,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * PMA project
 * Show - Hide fields based on ""Type (SL)"" custom field
 */
console.log(""Behaviours PMA Project – Type (SL) started."");


let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

let typeSL = getFieldById(getFieldChanged());

// This gets the value of ""Type (SL)"" as String
let typeSLOption = typeSL.getValue() as String

let isThisaGMConsultsIncludedRequest = getFieldById(""Is this a GM Consults included request?"");

switch (typeSLOption){
    case ""Chronic Care Management (CCM)"":
        isThisaGMConsultsIncludedRequest.setVisible(false);
    break;
    case ""Telemedicine"":
        isThisaGMConsultsIncludedRequest.setVisible(true);
    break;
    case ""Both Telemedicine & Chronic Care Management (CCM)"":
        isThisaGMConsultsIncludedRequest.setVisible(true);
    break;
    default:
        isThisaGMConsultsIncludedRequest.setVisible(false);
    break;
}",
14,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * PMA project
 * Show - Hide fields based on ""Are there any products that need to be suppressed?"" custom field
 */
console.log(""Behaviours PMA Project – Do group(s) need to be supressed? started."");


let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

let areThereAnyProductsThatNeedToBeSuppressed = getFieldById(getFieldChanged());

// This gets the value of ""Do group(s) need to be supressed?"" as String
let areThereAnyProductsThatNeedToBeSuppressedOption = areThereAnyProductsThatNeedToBeSuppressed.getValue() as String

let productName = getFieldById(""Product Name"");

if (areThereAnyProductsThatNeedToBeSuppressedOption == ""Yes""){
    productName.setVisible(true);
    productName.setRequired(true);
}
else{
    productName.setVisible(false);
    productName.setRequired(false);
}",
15,"// Had Imports
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// com.atlassian.jira.component.ComponentAccessor
// org.apache.log4j.Logger
//Creator: Jeff Tompkins
//Purpose: Show other fields dependent on this one
//Change log:

//imports
let customFieldManager = ComponentAccessor.getCustomFieldManager()
let attachmentManager = ComponentAccessor.getAttachmentManager()

@BaseScript FieldBehaviours fieldBehaviours

log = Logger.getLogger(""Behaviour: HHS Release Field Logic"")

let projectName = issueContext?.projectObject?.name
let changedFieldValue = getFieldById(getFieldChanged())?.getValue()

if( changedFieldValue == ""Yes""){
	getFieldById(""Release Notes Title"")?.setRequired(true)
	getFieldById(""Release Notes"")?.setRequired(true)
	getFieldById(""Customer Engagement"")?.setRequired(true)
	getFieldById(""Release Method"")?.setRequired(true)
	getFieldById(""Feature Toggle Name"")?.setRequired(true)
	getFieldById(""Release Images Included"")?.setRequired(true)
	getFieldById(""Region"")?.setRequired(true)


}else{
	getFieldById(""Release Notes Title"")?.setRequired(false)
	getFieldById(""Release Notes"")?.setRequired(false)
	getFieldById(""Customer Engagement"")?.setRequired(false)
	getFieldById(""Release Method"")?.setRequired(false)
	getFieldById(""Feature Toggle Name"")?.setRequired(false)
	getFieldById(""Release Images Included"")?.setRequired(false)
	getFieldById(""Region"")?.setRequired(false)
}

log.debug(""${projectName}-'Behaviour: HHS Release Field Logic hascompleted'"")",
16,"// Had Imports
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
//Creator: Jeff Tompkins
//Purpose: Show other fields dependent on this one

//imports

@BaseScript FieldBehaviours fieldBehaviours

let changedFieldValue = getFieldById(getFieldChanged())?.getValue()

if( changedFieldValue == ""Product"" || changedFieldValue == ""Superadmin practice configuration"" || changedFieldValue == ""Integration Setting""){
	getFieldById(""Release Method Rationale"")?.setVisible(true)
    getFieldById(""Release Method Rationale"")?.setRequired(true)
}else{
	getFieldById(""Release Method Rationale"")?.setVisible(false)
    getFieldById(""Release Method Rationale"")?.setRequired(false)
}",
17,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.customfields.option.Option
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// com.atlassian.jira.issue.MutableIssue
/**
 * Author: Jeff Tompkins
 * Purpose: Sets the RUN allocation fields based on Project or Project Category if the parent Epic is ""Supporting""
 * Log changes: 
*/


@BaseScript FieldBehaviours fieldBehaviours
let projectName = issueContext.projectObject.name

log.debug(""Behaviours: ${projectName} - Auto-populate RUN Fields Initialiser started."")

// Managers
let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

// Get the Field Names
let engDepartment = getFieldById(""Eng - Department"");
let engPlatform = getFieldById(""Eng - Platform"");
let deployedLocation = getFieldById(""Deployed Location"");
let deployedProduct = getFieldById(""Deployed Product"");
let runType = getFieldById(""RUN Type"");
let featureToggle = getFieldById(""Feature Toggle"");

// Main Script Body

// If this is a new issue being created, or an existing issue being edited
if(getAction().toString() == ""Create"" || getActionName() == null) {

	// Monitor the Epic Link field for any changes
	let changedFieldValue = getFieldById(getFieldChanged())?.getValue()
	let epicLinkKey = changedFieldValue.toString().substring(4)

	// If the Epic Link field is set
	if(changedFieldValue != null){
    
    	// Get the value of the ""Build/Run"" custom field for the linked Epic
		let issueManager = ComponentAccessor.getIssueManager()
		let parentIssue = issueManager.getIssueObject(epicLinkKey)
    	let buildRunField = customFieldManager.getCustomFieldObjectsByName(""Build/Run"")
		let parentMutableIssue = (MutableIssue) parentIssue // cast parentIssue to MutableIssue interface
    	let buildRunValue = parentMutableIssue.getCustomFieldValue(buildRunField[0])

		// If the Epic's ""Build/Run"" field is ""Supporting""
		if (buildRunValue.toString() == ""Supporting"") {

            // If the Project belongs to the Marketing team:  DEPG, DSPG, DSPGCCM (JIRA-9669)
            if ((issueContext?.getProjectObject().getName().includes(""Data Science Product Growth"")) || (issueContext?.getProjectObject().getName().includes(""Data Engineering Product Growth""))) {
                // If the ""Eng - Department"" field is not set
                if (!engDepartment.getValue()) {
                    engDepartment.setValue(""Data & AI"")
                }
                // If the ""Eng - Platform"" field is not set
                if (!engPlatform.getValue()) {
                    engPlatform.setValue(""Data & AI"")
                }
                // If the ""Run Type"" field is not set
                if (!runType.getValue()) {
                    runType.setValue(""Performance and Operational Improvements"")
                }
                // If the ""Deployed Product"" field is not set
                if (!deployedProduct.getValue()) {
                    if (issueContext?.getProjectObject().getName().includes(""Data Science Product Growth CCM"")) {
                        deployedProduct.setValue(""CCM"")
                    } else {
                        deployedProduct.setValue(""Global Telemed"")
                    }
                }
                // If the ""Deployed Location"" field is not set
                if (!deployedLocation.getValue()) {
                    deployedLocation.setValue(""US"")
                }
                // If the ""Feature Toggle"" field is not set
                if (!featureToggle.getValue()) {
                    featureToggle.setValue(""NO"")
                }
            }

            // If the Project belongs to the UCS team:  UCSA, UCSI, UCSS, UCSW (JIRA-9674)
            if (issueContext?.getProjectObject().getName().includes(""UCS"")) {
                // If the ""Eng - Department"" field is not set
                if (!engDepartment.getValue()) {
                    engDepartment.setValue(""Clinical"")
                }
                // If the ""Eng - Platform"" field is not set
                if (!engPlatform.getValue()) {
                    engPlatform.setValue(""Clinical"")
                }
                // If the ""Run Type"" field is not set
                if (!runType.getValue()) {
                    runType.setValue(""Performance and Operational Improvements"")
                }
                // If the ""Deployed Product"" field is not set
                if (!deployedProduct.getValue()) {
                    deployedProduct.setValue(""HHS"")
                }
                // If the ""Deployed Location"" field is not set
                if (!deployedLocation.getValue()) {
                    deployedLocation.setValue(""US"")
                }
            }

            // If the Project is MEX  (JIRA-9749)
            if (issueContext?.getProjectObject().getKey().includes(""MEX"")) {
                // If the ""Eng - Department"" field is not set
                if (!engDepartment.getValue()) {
                    engDepartment.setValue(""Consumer"")
                }
                // If the ""Eng - Platform"" field is not set
                if (!engPlatform.getValue()) {
                    engPlatform.setValue(""Consumer"")
                }
                // If the ""Run Type"" field is not set
                if (!runType.getValue()) {
                    runType.setValue(""Production Support"")
                }
                // If the ""Deployed Product"" field is not set
                if (!deployedProduct.getValue()) {
                    deployedProduct.setValue(""Global Telemed"")
                }
                // If the ""Deployed Location"" field is not set
                if (!deployedLocation.getValue()) {
                    deployedLocation.setValue(""US"")
                }
            }

        }

    }

}

console.log(""Behaviours: ${projectName} - Auto-populate RUN Fields Initialiser completed."")",
18,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.MutableIssue
// org.apache.log4j.Logger
// org.apache.log4j.Level
/*
Creator: Jeff Tompkins
Purpose: If an issue's parent Epic has the ""Build/Run"" field set to ""Supporting"", then make the 5 Run Allocation fields visible and required
The 2 ""Eng"" fields, ""Eng - Platform"" and ""Eng - Department"" will ALWAYS be visible for Stories and Bugs, but will be REQUIRED for RUN Epics.
*/

// import com.atlassian.jira.issue.fields.CustomField
// import com.atlassian.jira.issue.Issue

let log = Logger.getLogger(""Behaviour: Run Allocation Field Logic"")
log.setLevel(Level.INFO)

let projectName = issueContext.projectObject.name
log.debug(""Behaviour Script '${projectName} - Hide Run Allocation Fields' has started."")

// If this is a new issue being created
if(getAction().toString() == ""Create"") {

	// Monitor the Epic Link field for any changes
	let changedFieldValue = getFieldById(getFieldChanged())?.getValue()
	let epicLinkKey = changedFieldValue.toString().substring(4)

	// If the Epic Link field is set
	if( changedFieldValue != null){

		// Get the value of the ""Build/Run"" custom field for the linked Epic
		let issueManager = ComponentAccessor.getIssueManager()
		let customFieldManager = ComponentAccessor.getCustomFieldManager()
		let parentIssue = issueManager.getIssueObject(epicLinkKey)
    	let buildRunField = customFieldManager.getCustomFieldObjectsByName(""Build/Run"")
		let parentMutableIssue = (MutableIssue) parentIssue // cast parentIssue to MutableIssue interface
    	let buildRunValue = parentMutableIssue.getCustomFieldValue(buildRunField[0])

		// If the Epic's ""Build/Run"" field is ""Supporting""
		if (buildRunValue.toString() == ""Supporting"") {

			// Show the Run Allocation fields and set them as required
			getFieldById(""Eng - Department"").setVisible(true).setRequired(true)
			getFieldById(""Eng - Platform"").setVisible(true).setRequired(true)
			getFieldById(""RUN Type"").setVisible(true).setRequired(true)
			getFieldById(""Deployed Location"").setVisible(true).setRequired(true)
			getFieldById(""Deployed Product"").setVisible(true).setRequired(true)

		// If the Epic's ""Build/Run"" field is not ""Supporting""
		} else {

			// Hide the Run Allocation fields, clear their values, and make them optional
			getFieldById(""Eng - Department"").setVisible(true).setRequired(false).setValue(null)
			getFieldById(""Eng - Platform"").setVisible(true).setRequired(false).setValue(null)
			getFieldById(""RUN Type"").setVisible(false).setRequired(false).setValue(null)
			getFieldById(""Deployed Location"").setVisible(false).setRequired(false).setValue(null)
			getFieldById(""Deployed Product"").setVisible(false).setRequired(false).setValue(null)
		}

	// If the Epic Link field is not set
	} else {

		// Hide the Run Allocation fields, clear their values, and make them optional
		getFieldById(""Eng - Department"").setVisible(true).setRequired(false).setValue(null)
		getFieldById(""Eng - Platform"").setVisible(true).setRequired(false).setValue(null)
		getFieldById(""RUN Type"").setVisible(false).setRequired(false).setValue(null)
		getFieldById(""Deployed Location"").setVisible(false).setRequired(false).setValue(null)
		getFieldById(""Deployed Product"").setVisible(false).setRequired(false).setValue(null)
	}

// If this is an existing issue being edited
} else {

	// Hide the Run Allocation fields and make them optional
	getFieldById(""Eng - Department"").setVisible(true).setRequired(false)
	getFieldById(""Eng - Platform"").setVisible(true).setRequired(false)
	getFieldById(""RUN Type"").setVisible(false).setRequired(false)
	getFieldById(""Deployed Location"").setVisible(false).setRequired(false)
	getFieldById(""Deployed Product"").setVisible(false).setRequired(false)

	// Monitor the Epic Link field for any changes
	let changedFieldValue = getFieldById(getFieldChanged()).getValue()
	let epicLinkKey = changedFieldValue.toString().substring(4)

	// If the Epic Link field is set
	if( changedFieldValue != null){

		// Get the value of the ""Build/Run"" custom field for the linked Epic
		let issueManager = ComponentAccessor.getIssueManager()
		let customFieldManager = ComponentAccessor.getCustomFieldManager()
		let parentIssue = issueManager.getIssueObject(epicLinkKey)
    	let buildRunField = customFieldManager.getCustomFieldObjectsByName(""Build/Run"")
		let parentMutableIssue = (MutableIssue) parentIssue // cast parentIssue to MutableIssue interface
    	let buildRunValue = parentMutableIssue.getCustomFieldValue(buildRunField[0])

		// Set the visibility of target fields based on the value of the ""Build/Run"" field
		if (buildRunValue.toString() == ""Supporting"") {

			// Show the Run Allocation fields and set them as required
			getFieldById(""Eng - Department"").setVisible(true).setRequired(true)
			getFieldById(""Eng - Platform"").setVisible(true).setRequired(true)
			getFieldById(""RUN Type"").setVisible(true).setRequired(true)
			getFieldById(""Deployed Location"").setVisible(true).setRequired(true)
			getFieldById(""Deployed Product"").setVisible(true).setRequired(true)
		}

	// If the Epic Link field is not set
	} else {

		// Hide the Run Allocation fields, clear their values, and make them optional
		getFieldById(""Eng - Department"").setVisible(true).setRequired(false).setValue(null)
		getFieldById(""Eng - Platform"").setVisible(true).setRequired(false).setValue(null)
		getFieldById(""RUN Type"").setVisible(false).setRequired(false).setValue(null)
		getFieldById(""Deployed Location"").setVisible(false).setRequired(false).setValue(null)
		getFieldById(""Deployed Product"").setVisible(false).setRequired(false).setValue(null)
	}
}

log.debug(""Behaviour Script '${projectName} - Hide Run Allocation Fields' has completed."")",