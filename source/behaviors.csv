INDEX,script_to,Final Script,Cloud Script
1,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.MutableIssue
// org.apache.log4j.Logger
// org.apache.log4j.Level
/*
Creator: <PERSON>pose: If an issue's parent Epic has the ""Build/Run"" field set to ""Supporting"", then make the 5 Run Allocation fields visible and required
Change log:
*/

// import com.atlassian.jira.issue.fields.CustomField
// import com.atlassian.jira.issue.Issue

let log = Logger.getLogger(""Behaviour: Run Allocation Field Logic"")
log.setLevel(Level.INFO)

let projectName = issueContext.projectObject.name
log.debug(""Behaviour Script '${projectName} - Hide Run Allocation Fields' has started."")

// If this is a new issue being created
if(getAction().toString() == ""Create"") {

        // Monitor the Epic Link field for any changes
        let changedFieldValue = getFieldById(getFieldChanged())?.getValue()
        let epicLinkKey = changedFieldValue.toString().substring(4)

        // If the Epic Link field is set
        if( changedFieldValue != null){

                // Get the value of the ""Build/Run"" custom field for the linked Epic
                let issueManager = ComponentAccessor.getIssueManager()
                let customFieldManager = ComponentAccessor.getCustomFieldManager()
                let parentIssue = issueManager.getIssueObject(epicLinkKey)
            let buildRunField = customFieldManager.getCustomFieldObjectsByName(""Build/Run"")
                let parentMutableIssue = (MutableIssue) parentIssue // cast parentIssue to MutableIssue interface
            let buildRunValue = parentMutableIssue.getCustomFieldValue(buildRunField[0])

                // If the Epic's ""Build/Run"" field is not ""Business""
                if (buildRunValue.toString() != ""Business"") {

                        // Show the Run Allocation fields and set them as required
                        getFieldById(""Eng - Department"").setVisible(true).setRequired(true)
                        getFieldById(""Eng - Platform"").setVisible(true).setRequired(true)
                        getFieldById(""RUN Type"").setVisible(true).setRequired(true)
                        getFieldById(""Deployed Location"").setVisible(true).setRequired(true)
                        getFieldById(""Deployed Product"").setVisible(true).setRequired(true)

                // If the Epic's ""Build/Run"" field is ""Business""
                } else {

                        // Hide the Run Allocation fields, clear their values, and make them optional
                        getFieldById(""Eng - Department"").setVisible(false).setRequired(false).setValue(null)
                        getFieldById(""Eng - Platform"").setVisible(false).setRequired(false).setValue(null)
                        getFieldById(""RUN Type"").setVisible(false).setRequired(false).setValue(null)
                        getFieldById(""Deployed Location"").setVisible(false).setRequired(false).setValue(null)
                        getFieldById(""Deployed Product"").setVisible(false).setRequired(false).setValue(null)
                }

        // If the Epic Link field is not set
        } else {

                // Hide the Run Allocation fields, clear their values, and make them optional
                getFieldById(""Eng - Department"").setVisible(false).setRequired(false).setValue(null)
                getFieldById(""Eng - Platform"").setVisible(false).setRequired(false).setValue(null)
                getFieldById(""RUN Type"").setVisible(false).setRequired(false).setValue(null)
                getFieldById(""Deployed Location"").setVisible(false).setRequired(false).setValue(null)
                getFieldById(""Deployed Product"").setVisible(false).setRequired(false).setValue(null)
        }

// If this is an existing issue being edited
} else {

        // Hide the Run Allocation fields and make them optional
        getFieldById(""Eng - Department"").setVisible(false).setRequired(false)
        getFieldById(""Eng - Platform"").setVisible(false).setRequired(false)
        getFieldById(""RUN Type"").setVisible(false).setRequired(false)
        getFieldById(""Deployed Location"").setVisible(false).setRequired(false)
        getFieldById(""Deployed Product"").setVisible(false).setRequired(false)

        // Monitor the Epic Link field for any changes
        let changedFieldValue = getFieldById(getFieldChanged()).getValue()
        let epicLinkKey = changedFieldValue.toString().substring(4)

        // If the Epic Link field is set
        if( changedFieldValue != null){

                // Get the value of the ""Build/Run"" custom field for the linked Epic
                let issueManager = ComponentAccessor.getIssueManager()
                let customFieldManager = ComponentAccessor.getCustomFieldManager()
                let parentIssue = issueManager.getIssueObject(epicLinkKey)
            let buildRunField = customFieldManager.getCustomFieldObjectsByName(""Build/Run"")
                let parentMutableIssue = (MutableIssue) parentIssue // cast parentIssue to MutableIssue interface
            let buildRunValue = parentMutableIssue.getCustomFieldValue(buildRunField[0])

                // Set the visibility of target fields based on the value of the ""Build/Run"" field
                if (buildRunValue.toString() != ""Business"") {

                        // Show the Run Allocation fields and set them as required
                        getFieldById(""Eng - Department"").setVisible(true).setRequired(true)
                        getFieldById(""Eng - Platform"").setVisible(true).setRequired(true)
                        getFieldById(""RUN Type"").setVisible(true).setRequired(true)
                        getFieldById(""Deployed Location"").setVisible(true).setRequired(true)
                        getFieldById(""Deployed Product"").setVisible(true).setRequired(true)
                }

        // If the Epic Link field is not set
        } else {

                // Hide the Run Allocation fields, clear their values, and make them optional
                getFieldById(""Eng - Department"").setVisible(false).setRequired(false).setValue(null)
                getFieldById(""Eng - Platform"").setVisible(false).setRequired(false).setValue(null)
                getFieldById(""RUN Type"").setVisible(false).setRequired(false).setValue(null)
                getFieldById(""Deployed Location"").setVisible(false).setRequired(false).setValue(null)
                getFieldById(""Deployed Product"").setVisible(false).setRequired(false).setValue(null)
        }
}

log.debug(""Behaviour Script '${projectName} - Hide Run Allocation Fields' has completed."")","// Do not Edit
const context = await getContext();

// Utility: Fetch issue by key
async function getIssueByKey(ikey) {
    const res = await makeRequest(""/rest/api/2/issue/"" + ikey);
    console.log(res.body.key, res.body.fields)
    return res.body;
}

if (getChangeField().getName() === ""Parent"") {
    const changedField = getChangeField();

    const parentValue = changedField.getValue().key;
    const parentIssue = await getIssueByKey(parentValue);
    const buildRunValue = parentIssue.fields.customfield_10093?.value; // Assumes ""Build/Run"" has this ID

    if (buildRunValue != ""Business"") {

        getFieldById(""customfield_10126"").setVisible(true); // Field (Eng - Department)
        getFieldById(""customfield_10126"").setRequired(true); // Field (Eng - Department)

        getFieldById(""customfield_10067"").setVisible(true); // Field (Eng - Platform)
        getFieldById(""customfield_10067"").setRequired(true); // Field (Eng - Platform)

        getFieldById(""customfield_10086"").setVisible(true); // Field (RUN Type)
        getFieldById(""customfield_10086"").setRequired(true); // Field (RUN Type)

        getFieldById(""customfield_10096"").setVisible(true); // Field (Deployed Location)
        getFieldById(""customfield_10096"").setRequired(true); // Field (Deployed Location)
        
        getFieldById(""customfield_10068"").setVisible(true); // Field (Deployed Product)
        getFieldById(""customfield_10068"").setRequired(true); // Field (Deployed Product)
    
    } else {
        
        getFieldById(""customfield_10126"").setVisible(false); // Field (Eng - Department)
        getFieldById(""customfield_10126"").setRequired(false); // Field (Eng - Department)

        getFieldById(""customfield_10067"").setVisible(false); // Field (Eng - Platform)
        getFieldById(""customfield_10067"").setRequired(false); // Field (Eng - Platform)

        getFieldById(""customfield_10086"").setVisible(false); // Field (RUN Type)
        getFieldById(""customfield_10086"").setRequired(false); // Field (RUN Type)

        getFieldById(""customfield_10096"").setVisible(false); // Field (Deployed Location)
        getFieldById(""customfield_10096"").setRequired(false); // Field (Deployed Location)
        
        getFieldById(""customfield_10068"").setVisible(false); // Field (Deployed Product)
        getFieldById(""customfield_10068"").setRequired(false); // Field (Deployed Product)

    }


}","/*
Creator: Jeff Tompkins
Purpose: If an issue's parent Epic has the ""Build/Run"" field set to ""Supporting"", then make the 5 Run Allocation fields visible and required
Change log: Rewritten for Jira Cloud compatibility
*/

// Access the context for the UI Modification
const context = await getContext();

console.log(""Behaviour Script 'Run Allocation Field Logic' has started."");

// Utility: Fetch issue by key
async function getIssueByKey(ikey) {
    const res = await makeRequest(""/rest/api/2/issue/"" + ikey);
    console.log(res.body.key, res.body.fields);
    return res.body;
}

// Check if Parent field has changed
if (getChangeField().getName() === ""Parent"") {
    const changedField = getChangeField();

    if (changedField.getValue() && changedField.getValue().key) {
        const parentValue = changedField.getValue().key;
        const parentIssue = await getIssueByKey(parentValue);
        const buildRunValue = parentIssue.fields.customfield_10093?.value; // Field (Build/Run)

        console.log(""Parent Issue Build/Run value:"", buildRunValue);

        if (buildRunValue && buildRunValue !== ""Business"") {
            // Show the Run Allocation fields and set them as required
            getFieldById(""customfield_10126"").setVisible(true).setRequired(true); // Field (Eng - Department)
            getFieldById(""customfield_10067"").setVisible(true).setRequired(true); // Field (Eng - Platform)
            getFieldById(""customfield_10086"").setVisible(true).setRequired(true); // Field (RUN Type)
            getFieldById(""customfield_10096"").setVisible(true).setRequired(true); // Field (Deployed Location)
            getFieldById(""customfield_10068"").setVisible(true).setRequired(true); // Field (Deployed Product)
        } else {
            // Hide the Run Allocation fields and make them optional
            getFieldById(""customfield_10126"").setVisible(false).setRequired(false); // Field (Eng - Department)
            getFieldById(""customfield_10067"").setVisible(false).setRequired(false); // Field (Eng - Platform)
            getFieldById(""customfield_10086"").setVisible(false).setRequired(false); // Field (RUN Type)
            getFieldById(""customfield_10096"").setVisible(false).setRequired(false); // Field (Deployed Location)
            getFieldById(""customfield_10068"").setVisible(false).setRequired(false); // Field (Deployed Product)
        }
    } else {
        // If no parent is set, hide the Run Allocation fields
        getFieldById(""customfield_10126"").setVisible(false).setRequired(false); // Field (Eng - Department)
        getFieldById(""customfield_10067"").setVisible(false).setRequired(false); // Field (Eng - Platform)
        getFieldById(""customfield_10086"").setVisible(false).setRequired(false); // Field (RUN Type)
        getFieldById(""customfield_10096"").setVisible(false).setRequired(false); // Field (Deployed Location)
        getFieldById(""customfield_10068"").setVisible(false).setRequired(false); // Field (Deployed Product)
    }
}

console.log(""Behaviour Script 'Run Allocation Field Logic' has completed."");"
2, ,"// Do not Edit
const context = await getContext();

getFieldById(""customfield_10126"").setVisible(false); // Field (Eng - Department)
getFieldById(""customfield_10126"").setRequired(false); // Field (Eng - Department)

getFieldById(""customfield_10067"").setVisible(false); // Field (Eng - Platform)
getFieldById(""customfield_10067"").setRequired(false); // Field (Eng - Platform)

getFieldById(""customfield_10086"").setVisible(false); // Field (RUN Type)
getFieldById(""customfield_10086"").setRequired(false); // Field (RUN Type)

getFieldById(""customfield_10096"").setVisible(false); // Field (Deployed Location)
getFieldById(""customfield_10096"").setRequired(false); // Field (Deployed Location)

getFieldById(""customfield_10068"").setVisible(false); // Field (Deployed Product)
getFieldById(""customfield_10068"").setRequired(false); // Field (Deployed Product)


// Utility: Fetch issue by key
async function getIssueByKey(ikey) {
    const res = await makeRequest(""/rest/api/2/issue/"" + ikey);
    console.log(res.body.key, res.body.fields)
    return res.body;
}

if (getChangeField().getName() === ""Parent"") {

    const changedField = getChangeField();

    const parentValue = changedField.getValue().key;
    const parentIssue = await getIssueByKey(parentValue);
    const buildRunValue = parentIssue.fields.customfield_10093?.value; // Assumes ""Build/Run"" has this ID

    if (buildRunValue != ""Business"") {

        getFieldById(""customfield_10126"").setVisible(true); // Field (Eng - Department)
        getFieldById(""customfield_10126"").setRequired(true); // Field (Eng - Department)

        getFieldById(""customfield_10067"").setVisible(true); // Field (Eng - Platform)
        getFieldById(""customfield_10067"").setRequired(true); // Field (Eng - Platform)

        getFieldById(""customfield_10086"").setVisible(true); // Field (RUN Type)
        getFieldById(""customfield_10086"").setRequired(true); // Field (RUN Type)

        getFieldById(""customfield_10096"").setVisible(true); // Field (Deployed Location)
        getFieldById(""customfield_10096"").setRequired(true); // Field (Deployed Location)
        
        getFieldById(""customfield_10068"").setVisible(true); // Field (Deployed Product)
        getFieldById(""customfield_10068"").setRequired(true); // Field (Deployed Product)
    
    } else {
        
        getFieldById(""customfield_10126"").setVisible(false); // Field (Eng - Department)
        getFieldById(""customfield_10126"").setRequired(false); // Field (Eng - Department)

        getFieldById(""customfield_10067"").setVisible(false); // Field (Eng - Platform)
        getFieldById(""customfield_10067"").setRequired(false); // Field (Eng - Platform)

        getFieldById(""customfield_10086"").setVisible(false); // Field (RUN Type)
        getFieldById(""customfield_10086"").setRequired(false); // Field (RUN Type)

        getFieldById(""customfield_10096"").setVisible(false); // Field (Deployed Location)
        getFieldById(""customfield_10096"").setRequired(false); // Field (Deployed Location)
        
        getFieldById(""customfield_10068"").setVisible(false); // Field (Deployed Product)
        getFieldById(""customfield_10068"").setRequired(false); // Field (Deployed Product)

    }
}
","/*
Purpose: Default behavior to hide Run Allocation fields and handle Parent field changes
Change log: Rewritten for Jira Cloud compatibility
*/

// Access the context for the UI Modification
const context = await getContext();

console.log(""Behaviour Script 'Default Run Allocation Fields' has started."");

// Initially hide all Run Allocation fields
getFieldById(""customfield_10126"").setVisible(false).setRequired(false); // Field (Eng - Department)
getFieldById(""customfield_10067"").setVisible(false).setRequired(false); // Field (Eng - Platform)
getFieldById(""customfield_10086"").setVisible(false).setRequired(false); // Field (RUN Type)
getFieldById(""customfield_10096"").setVisible(false).setRequired(false); // Field (Deployed Location)
getFieldById(""customfield_10068"").setVisible(false).setRequired(false); // Field (Deployed Product)

// Utility: Fetch issue by key
async function getIssueByKey(ikey) {
    const res = await makeRequest(""/rest/api/2/issue/"" + ikey);
    console.log(res.body.key, res.body.fields);
    return res.body;
}

// Check if Parent field has changed
if (getChangeField().getName() === ""Parent"") {
    const changedField = getChangeField();

    if (changedField.getValue() && changedField.getValue().key) {
        const parentValue = changedField.getValue().key;
        const parentIssue = await getIssueByKey(parentValue);
        const buildRunValue = parentIssue.fields.customfield_10093?.value; // Field (Build/Run)

        console.log(""Parent Issue Build/Run value:"", buildRunValue);

        if (buildRunValue && buildRunValue !== ""Business"") {
            // Show the Run Allocation fields and set them as required
            getFieldById(""customfield_10126"").setVisible(true).setRequired(true); // Field (Eng - Department)
            getFieldById(""customfield_10067"").setVisible(true).setRequired(true); // Field (Eng - Platform)
            getFieldById(""customfield_10086"").setVisible(true).setRequired(true); // Field (RUN Type)
            getFieldById(""customfield_10096"").setVisible(true).setRequired(true); // Field (Deployed Location)
            getFieldById(""customfield_10068"").setVisible(true).setRequired(true); // Field (Deployed Product)
        }
    }
}

console.log(""Behaviour Script 'Default Run Allocation Fields' has completed."");"
3,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * CRUS project - Client Reporting - USGH
 * Issue Type: Story, Task, Bug
 * Hide fields based on ""Issue Type"" Question
 */
console.log(""Behaviours CRUS Project – Issue Type started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();


// This listens if the ""Issue Type"" field has changed
let issueType = getFieldById(getFieldChanged());
// This saves custom field's ids to manage its behaviors
let requestType = getFieldById(""Request Type"");
let newOrEnhancement = getFieldById(""New or Enhancement"");
let standardDataExtractType = getFieldById(""Standard Data Extract Type"");
let reportName = getFieldById(""Report Name"");
let description = getFieldById(""Description"");
let businessRequirements = getFieldById(""Business Requirements"");
let areThereAnyTags = getFieldById(""Are there any tags?"");
let clientName = getFieldById(""Client Name"");
let clientLaunchDate = getFieldById(""Client Launch Date"");
let contractLanguageForGrievanceReporting = getFieldById(""Contract Language for Grievance Reporting"");
let clientLevel = getFieldById(""Client Level"");
let sensitiveInformation = getFieldById(""Sensitive Information"");
let fileTypeDelimiter = getFieldById(""File Type Delimiter"");

console.log(""Issue Type: "" + issueContext.issueType.name);

switch(issueContext.issueType.name){
    case ""Task"":
            reportName.setVisible(false);
                reportName.setRequired(false);
            description.setVisible(true);
            description.setRequired(true);
            clientLevel.setRequired(false);
            sensitiveInformation.setVisible(true);
            sensitiveInformation.setRequired(false);
            fileTypeDelimiter.setVisible(true);
            break;
    case ""Story"":
            requestType.setRequired(true);
            newOrEnhancement.setVisible(false);
            standardDataExtractType.setVisible(false);
                standardDataExtractType.setRequired(false);
                areThereAnyTags.setVisible(false);
                areThereAnyTags.setRequired(false);
                clientName.setVisible(false);
                clientName.setRequired(false);
                //clientLaunchDate.setVisible(false); // updated (JIRA-5208)
                clientLaunchDate.setRequired(false);
                contractLanguageForGrievanceReporting.setVisible(false);
                contractLanguageForGrievanceReporting.setRequired(false);
                description.setVisible(true);
                description.setRequired(true);
                businessRequirements.setRequired(true);
            break;
    case ""Bug"":
            reportName.setVisible(true);
                reportName.setRequired(true);
            description.setVisible(true);
            description.setRequired(false);
            clientLevel.setRequired(false);
            sensitiveInformation.setVisible(true);
            sensitiveInformation.setRequired(false);
            fileTypeDelimiter.setVisible(false);
            break;
    default:
        break;
}
console.log(""Behaviours CRUS Project – Issue Type completed."") ","/*
Creator: Eduardo Rojo
Purpose: CRUS project - Client Reporting - USGH - Hide fields based on Issue Type
Change log: Rewritten for Jira Cloud compatibility
*/

// Access the context for the UI Modification
const context = await getContext();
const issueType = context.extension.issueType.name;

console.log(""Behaviours CRUS Project – Issue Type started."");
console.log(""Issue Type:"", issueType);

// Get field references with proper custom field IDs
const requestType = getFieldById(""customfield_10080""); // Field (Request Type)
const newOrEnhancement = getFieldById(""customfield_10081""); // Field (New or Enhancement)
const standardDataExtractType = getFieldById(""customfield_10082""); // Field (Standard Data Extract Type)
const reportName = getFieldById(""customfield_10083""); // Field (Report Name)
const description = getFieldById(""description""); // Field (Description)
const businessRequirements = getFieldById(""customfield_10084""); // Field (Business Requirements)
const areThereAnyTags = getFieldById(""customfield_10085""); // Field (Are there any tags?)
const clientName = getFieldById(""customfield_10086""); // Field (Client Name)
const clientLaunchDate = getFieldById(""customfield_10087""); // Field (Client Launch Date)
const contractLanguageForGrievanceReporting = getFieldById(""customfield_10088""); // Field (Contract Language for Grievance Reporting)
const clientLevel = getFieldById(""customfield_10089""); // Field (Client Level)
const sensitiveInformation = getFieldById(""customfield_10090""); // Field (Sensitive Information)
const fileTypeDelimiter = getFieldById(""customfield_10091""); // Field (File Type Delimiter)

switch(issueType) {
    case ""Task"":
        reportName.setVisible(false);
        reportName.setRequired(false);
        description.setVisible(true);
        description.setRequired(true);
        clientLevel.setRequired(false);
        sensitiveInformation.setVisible(true);
        sensitiveInformation.setRequired(false);
        fileTypeDelimiter.setVisible(true);
        break;

    case ""Story"":
        requestType.setRequired(true);
        newOrEnhancement.setVisible(false);
        standardDataExtractType.setVisible(false);
        standardDataExtractType.setRequired(false);
        areThereAnyTags.setVisible(false);
        areThereAnyTags.setRequired(false);
        clientName.setVisible(false);
        clientName.setRequired(false);
        clientLaunchDate.setRequired(false);
        contractLanguageForGrievanceReporting.setVisible(false);
        contractLanguageForGrievanceReporting.setRequired(false);
        description.setVisible(true);
        description.setRequired(true);
        businessRequirements.setRequired(true);
        break;

    case ""Bug"":
        reportName.setVisible(true);
        reportName.setRequired(true);
        description.setVisible(true);
        description.setRequired(false);
        clientLevel.setRequired(false);
        sensitiveInformation.setVisible(true);
        sensitiveInformation.setRequired(false);
        fileTypeDelimiter.setVisible(false);
        break;

    default:
        break;
}

console.log(""Behaviours CRUS Project – Issue Type completed."");"
// This listens if the ""Issue Type"" field has changed
let context = await getContext()
const issueType = context.extension.issueType.name

// This saves custom field's ids to manage its behaviors
let requestType = getFieldById(""customfield_10080""); // Field (Request Type)
let newOrEnhancement = getFieldById(""customfield_10080""); // Field (New or Enhancement)
let standardDataExtractType = getFieldById(""customfield_10080""); // Field (Standard Data Extract Type)
let reportName = getFieldById(""customfield_10080""); // Field (Report Name)
let description = getFieldById(""customfield_10080""); // Field (Description)
let businessRequirements = getFieldById(""customfield_10080""); // Field (Business Requirements)
let areThereAnyTags = getFieldById(""customfield_10080""); // Field (Are there any tags?)
let clientName = getFieldById(""customfield_10080""); // Field (Client Name)
let clientLaunchDate = getFieldById(""customfield_10080""); // Field (Client Launch Date)
let contractLanguageForGrievanceReporting = getFieldById(""customfield_10080""); // Field (Contract Language for Grievance Reporting)
let clientLevel = getFieldById(""customfield_10080""); // Field (Client Level)
let sensitiveInformation = getFieldById(""customfield_10080""); // Field (Sensitive Information)
let fileTypeDelimiter = getFieldById(""customfield_10080""); // Field (File Type Delimiter)

switch(issueType){
    case ""Task"":
            reportName.setVisible(false);
            reportName.setRequired(false);
            description.setVisible(true);
            description.setRequired(true);
            clientLevel.setRequired(false);
            sensitiveInformation.setVisible(true);
            sensitiveInformation.setRequired(false);
            fileTypeDelimiter.setVisible(true);
            break;
    case ""Story"":
            requestType.setRequired(true);
            newOrEnhancement.setVisible(false);
            standardDataExtractType.setVisible(false);
            standardDataExtractType.setRequired(false);
            areThereAnyTags.setVisible(false);
            areThereAnyTags.setRequired(false);
            clientName.setVisible(false);
            clientName.setRequired(false);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false);
            contractLanguageForGrievanceReporting.setRequired(false);
            description.setVisible(true);
            description.setRequired(true);
            businessRequirements.setRequired(true);
            break;
    case ""Bug"":
            reportName.setVisible(true);
            reportName.setRequired(true);
            description.setVisible(true);
            description.setRequired(false);
            clientLevel.setRequired(false);
            sensitiveInformation.setVisible(true);
            sensitiveInformation.setRequired(false);
            fileTypeDelimiter.setVisible(false);
            break;
    default:
        break;
}"
4,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * CRUS project - Client Reporting - USGH
 * Issue Type: Story
 * Hide fields based on ""New or Enhancement""
 * Log: 5/15/2023 Eduardo Rojo (JIRA-5208)
 *                6/5/2024 Eduardo Rojo (JIRA-9551)
 */

console.log(""Behaviours CRUS Project – New or Enhancement started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager()
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Report Type"" field has changed
let newOrEnhancement = getFieldById(getFieldChanged());
// This saves custom field's ids to manage its behaviors
let standardDataExtractType = getFieldById(""Standard Data Extract Type"");
let areThereAnyTags = getFieldById(""Are there any tags?"");
let clientName = getFieldById(""Client Name"");
let clientLaunchDate = getFieldById(""Client Launch Date"");
let contractLanguageForGrievanceReporting = getFieldById(""Contract Language for Grievance Reporting"");
let clientLevel = getFieldById(""Client Level"");
let businessRequirements = getFieldById(""Business Requirements"");
let isThisReusable = getFieldById(""Is this Reusable?"");
let reportingTeamOwner = getFieldById(""Reporting Team Owner"");
let approver = getFieldById(""Approver"");
let audience = getFieldById(""Audience (SL)"");
let audienceDetails = getFieldById(""Audience Details"");
let contractualObligation = getFieldById(""Contractual Obligation"");
let sOWorBRDRequired = getFieldById(""SOW or BRD required?"");
let sensitiveInformation = getFieldById(""Sensitive Information"");
//let dataSharingAgreement = getFieldById(""Data Sharing Agreement""); // updated (JIRA-5208)
let fileTypePGPEncryption = getFieldById(""File Type PGP Encryption"");
let fileTypeExtension = getFieldById(""File Type Extension"");
let fileTypeDelimiter = getFieldById(""File Type Delimiter"");
let extensionDetails = getFieldById(""Extension Details"");
let frequency = getFieldById(""Frequency"");
let modeOfDelivery = getFieldById(""Mode of Delivery"");
let modeOfDeliveryVendor = getFieldById(""SFTP - Client/Vendor Details"");
let modeOfDeliveryEmail = getFieldById(""Email Delivery - Provide Email"");
let priorityScore = getFieldById(""Priority Score"");
let standardAddOns = getFieldById(""Standard Add Ons"");//added JIRA-9551

// This gets the value of ""New or Enhancement""
let newOrEnhancementOption = newOrEnhancement.getValue() as String

// This resets the fields in case the user changes the ""Report Type""
standardDataExtractType.setVisible(false);
standardDataExtractType.setRequired(false);

console.log(""New or Enhancement: "" + newOrEnhancementOption);
switch(newOrEnhancementOption){
    case ""Standard - New"":
            // Show/Hide fields for ""Standar - New"" option
            standardDataExtractType.setVisible(true);
                standardDataExtractType.setRequired(true);
                areThereAnyTags.setVisible(true);
                areThereAnyTags.setRequired(true);
                //clientName.setVisible(false);
                //clientName.setRequired(false);
                //clientLaunchDate.setVisible(false); // updated (JIRA-5208)
                clientLaunchDate.setRequired(false);
                contractLanguageForGrievanceReporting.setVisible(false);
                contractLanguageForGrievanceReporting.setRequired(false); 
            // Show/Hide other fields
            clientLevel.setRequired(false);
            businessRequirements.setRequired(false);
            isThisReusable.setVisible(false);
                isThisReusable.setRequired(false); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
                //sOWorBRDRequired.setRequired(true); // updated (JIRA-5208)
            sensitiveInformation.setVisible(true);
                sensitiveInformation.setRequired(true);
            // dataSharingAgreement.setVisible(true); // updated (JIRA-5208)
                // dataSharingAgreement.setRequired(true); // updated (JIRA-5208)
                fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
                frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
                modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(false);
                standardAddOns.setVisible(true);//added JIRA-9551
            break;
    case ""Standard - Enhancement"":
            // Show/Hide fields for ""Standar - New"" option
            standardDataExtractType.setVisible(true);
                standardDataExtractType.setRequired(true);
                areThereAnyTags.setVisible(true);
                areThereAnyTags.setRequired(true);
                //clientName.setVisible(false);
                //clientName.setRequired(false);
                //clientLaunchDate.setVisible(false); // updated (JIRA-5208)
                clientLaunchDate.setRequired(false);
                contractLanguageForGrievanceReporting.setVisible(false);
                contractLanguageForGrievanceReporting.setRequired(false); 
            // Show/Hide other fields
            clientLevel.setRequired(false);
            businessRequirements.setRequired(false);
            isThisReusable.setVisible(false);
                isThisReusable.setRequired(false); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
                //sOWorBRDRequired.setRequired(true); // updated (JIRA-5208)
            sensitiveInformation.setVisible(true);
                sensitiveInformation.setRequired(true);
            // dataSharingAgreement.setVisible(true); // updated (JIRA-5208)
                // dataSharingAgreement.setRequired(true); // updated (JIRA-5208)
                fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
                frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
                modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(false);
                standardAddOns.setVisible(true);//added JIRA-9551
                /* ************COMMENTED JIRA-9551
                // Show/Hide fields for ""Standar - New"" option
            standardDataExtractType.setVisible(false);
                standardDataExtractType.setRequired(false);
                areThereAnyTags.setVisible(false);
                areThereAnyTags.setRequired(false);
                //clientName.setVisible(false);
                //clientName.setRequired(false);
                //clientLaunchDate.setVisible(false); // updated (JIRA-5208)
                clientLaunchDate.setRequired(false);
                contractLanguageForGrievanceReporting.setVisible(false);
                contractLanguageForGrievanceReporting.setRequired(false); 
            // Show/Hide fields for ""Standard - Enhancement"" option
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true);
                isThisReusable.setRequired(false); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
                //sOWorBRDRequired.setRequired(true); // updated (JIRA-5208)
            sensitiveInformation.setVisible(true);
                sensitiveInformation.setRequired(true);
            // dataSharingAgreement.setVisible(true); // updated (JIRA-5208)
                // dataSharingAgreement.setRequired(true); // updated (JIRA-5208)
                fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
                frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
                modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(true);
                standardAddOns.setVisible(false);//added JIRA-9551
            */
                break;
    case ""Custom - New"":
            // Show/Hide fields for ""Standar - New"" option
            standardDataExtractType.setVisible(false);
                standardDataExtractType.setRequired(false);
                areThereAnyTags.setVisible(false);
                areThereAnyTags.setRequired(false);
                //clientName.setVisible(false);
                //clientName.setRequired(false);
                //clientLaunchDate.setVisible(false); // updated (JIRA-5208)
                clientLaunchDate.setRequired(false);
                contractLanguageForGrievanceReporting.setVisible(false);
                contractLanguageForGrievanceReporting.setRequired(false); 
            // Show/Hide fields for ""Custom - New"" option
                clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true);
                isThisReusable.setRequired(true); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
                //sOWorBRDRequired.setRequired(true); // updated (JIRA-5208)
            sensitiveInformation.setVisible(true);
                sensitiveInformation.setRequired(true);
            // dataSharingAgreement.setVisible(true); // updated (JIRA-5208)
                // dataSharingAgreement.setRequired(true); // updated (JIRA-5208)
                fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
                frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
                modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(true);
                standardAddOns.setVisible(false);//added JIRA-9551
            break;
    case ""Custom - Enhancement"":
            // Show/Hide fields for ""Standar - New"" option
            standardDataExtractType.setVisible(false);
                standardDataExtractType.setRequired(false);
                areThereAnyTags.setVisible(false);
                areThereAnyTags.setRequired(false);
                //clientName.setVisible(false);
                //clientName.setRequired(false);
                //clientLaunchDate.setVisible(false); // updated (JIRA-5208)
                clientLaunchDate.setRequired(false);
                contractLanguageForGrievanceReporting.setVisible(false);
                contractLanguageForGrievanceReporting.setRequired(false); 
            // Show/Hide fields for ""Custom - Enhancement"" option
                clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true);
                isThisReusable.setRequired(false); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
                //sOWorBRDRequired.setRequired(true); // updated (JIRA-5208)
            sensitiveInformation.setVisible(true);
                sensitiveInformation.setRequired(true);
            // dataSharingAgreement.setVisible(true); // updated (JIRA-5208)
                // dataSharingAgreement.setRequired(true); // updated (JIRA-5208)
                fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
                frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
                modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(true);
                standardAddOns.setVisible(false);//added JIRA-9551
            break;
    case ""New"":
            // Show/Hide fields for ""New"" option
            standardDataExtractType.setVisible(false);
                standardDataExtractType.setRequired(false);
                areThereAnyTags.setVisible(false);
                areThereAnyTags.setRequired(false);
                //clientName.setVisible(false);
                //clientName.setRequired(false);
                //clientLaunchDate.setVisible(false); // updated (JIRA-5208)
                clientLaunchDate.setRequired(false);
                contractLanguageForGrievanceReporting.setVisible(false);
                contractLanguageForGrievanceReporting.setRequired(false); 
            // Show/Hide fields for ""New"" option
                clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(false);
                isThisReusable.setRequired(false); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
                //sOWorBRDRequired.setRequired(true); // updated (JIRA-5208)
            sensitiveInformation.setVisible(true);
                sensitiveInformation.setRequired(true);
            // dataSharingAgreement.setVisible(true); // updated (JIRA-5208)
                // dataSharingAgreement.setRequired(true); // updated (JIRA-5208)
                fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
                frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
                modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
                standardAddOns.setVisible(false);//added JIRA-9551
            break;
    case ""Enhancement"":
            // Show/Hide fields for ""Standar - New"" option
            standardDataExtractType.setVisible(false);
                standardDataExtractType.setRequired(false);
                areThereAnyTags.setVisible(false);
                areThereAnyTags.setRequired(false);
                //clientName.setVisible(false);
                //clientName.setRequired(false);
                //clientLaunchDate.setVisible(false); // updated (JIRA-5208)
                clientLaunchDate.setRequired(false);
                contractLanguageForGrievanceReporting.setVisible(false);
                contractLanguageForGrievanceReporting.setRequired(false); 
            // Show/Hide fields for ""Enhancement"" option
                clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true);
                isThisReusable.setRequired(false); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
                //sOWorBRDRequired.setRequired(true); // updated (JIRA-5208)
            sensitiveInformation.setVisible(true);
                sensitiveInformation.setRequired(true);
            // dataSharingAgreement.setVisible(true); // updated (JIRA-5208)
                // dataSharingAgreement.setRequired(true); // updated (JIRA-5208)
                fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
                frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
                modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
                standardAddOns.setVisible(false);//added JIRA-9551
            break;
    default:
                standardAddOns.setVisible(false);//added JIRA-9551
        break;
}
console.log(""Behaviours CRUS Project – New or Enhancement completed."");","let run = false
let data = null

if (getChangeField().getName() == ""New or Enhancement"") {
    run = true
    data = getChangeField().getValue().value
}

if (run) {

    // This saves custom field's ids to manage its behaviors
    let standardDataExtractType = getFieldById(""customfield_10089""); // Field (Standard Data Extract Type)
    let areThereAnyTags = getFieldById(""customfield_10089""); // Field (Are there any tags?)
    let clientName = getFieldById(""customfield_10089""); // Field (Client Name)
    let clientLaunchDate = getFieldById(""customfield_10089""); // Field (Client Launch Date)
    let contractLanguageForGrievanceReporting = getFieldById(""customfield_10089""); // Field (Contract Language for Grievance Reporting)
    let clientLevel = getFieldById(""customfield_10089""); // Field (Client Level)
    let businessRequirements = getFieldById(""customfield_10089""); // Field (Business Requirements)
    let isThisReusable = getFieldById(""customfield_10089""); // Field (Is this Reusable?)
    let reportingTeamOwner = getFieldById(""customfield_10089""); // Field (Reporting Team Owner)
    let approver = getFieldById(""customfield_10089""); // Field (Approver)
    let audience = getFieldById(""customfield_10089""); // Field (Audience (SL))
    let audienceDetails = getFieldById(""customfield_10089""); // Field (Audience Details)
    let contractualObligation = getFieldById(""customfield_10089""); // Field (Contractual Obligation)
    let sOWorBRDRequired = getFieldById(""customfield_10089""); // Field (SOW or BRD required?)
    let sensitiveInformation = getFieldById(""customfield_10089""); // Field (Sensitive Information)
    let fileTypePGPEncryption = getFieldById(""customfield_10089""); // Field (File Type PGP Encryption)
    let fileTypeExtension = getFieldById(""customfield_10089""); // Field (File Type Extension)
    let fileTypeDelimiter = getFieldById(""customfield_10089""); // Field (File Type Delimiter)
    let extensionDetails = getFieldById(""customfield_10089""); // Field (Extension Details)
    let frequency = getFieldById(""customfield_10089""); // Field (Frequency)
    let modeOfDelivery = getFieldById(""customfield_10089""); // Field (Mode of Delivery)
    let modeOfDeliveryVendor = getFieldById(""customfield_10089""); // Field (SFTP - Client/Vendor Details)
    let modeOfDeliveryEmail = getFieldById(""customfield_10089""); // Field (Email Delivery - Provide Email)
    let priorityScore = getFieldById(""customfield_10089""); // Field (Priority Score)
    let standardAddOns = getFieldById(""customfield_10089""); // Field (Standard Add Ons) - added JIRA-9551

    // This gets the value of ""New or Enhancement""
    let newOrEnhancementOption = data as String

    // This resets the fields in case the user changes the ""Report Type""
    standardDataExtractType.setVisible(false);
    standardDataExtractType.setRequired(false);

    switch(newOrEnhancementOption){
        case ""Standard - New"":
            // Show/Hide fields for ""Standar - New"" option
            standardDataExtractType.setVisible(true);
            standardDataExtractType.setRequired(true);
            areThereAnyTags.setVisible(true);
            areThereAnyTags.setRequired(true);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false);
            contractLanguageForGrievanceReporting.setRequired(false); 
            
            // Show/Hide other fields
            clientLevel.setRequired(false);
            businessRequirements.setRequired(false);
            isThisReusable.setVisible(false);
            isThisReusable.setRequired(false); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true);
            sensitiveInformation.setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
            frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
            modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(false);
            standardAddOns.setVisible(true);//added JIRA-9551
            break;

        case ""Standard - Enhancement"":
            // Show/Hide fields for ""Standar - New"" option
            standardDataExtractType.setVisible(true);
            standardDataExtractType.setRequired(true);
            areThereAnyTags.setVisible(true);
            areThereAnyTags.setRequired(true);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false);
            contractLanguageForGrievanceReporting.setRequired(false); 

            // Show/Hide other fields
            clientLevel.setRequired(false);
            businessRequirements.setRequired(false);
            isThisReusable.setVisible(false);
            isThisReusable.setRequired(false); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true);
            sensitiveInformation.setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
            frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
            modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(false);
            standardAddOns.setVisible(true); //added JIRA-9551
            break;

        case ""Custom - New"":
            // Show/Hide fields for ""Standar - New"" option
            standardDataExtractType.setVisible(false);
            standardDataExtractType.setRequired(false);
            areThereAnyTags.setVisible(false);
            areThereAnyTags.setRequired(false);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false);
            contractLanguageForGrievanceReporting.setRequired(false); 

            // Show/Hide fields for ""Custom - New"" option
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true);
            isThisReusable.setRequired(true); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true);
            sensitiveInformation.setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
            frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
            modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(true);
            standardAddOns.setVisible(false);//added JIRA-9551
            break;
        case ""Custom - Enhancement"":
            // Show/Hide fields for ""Standar - New"" option
            standardDataExtractType.setVisible(false);
            standardDataExtractType.setRequired(false);
            areThereAnyTags.setVisible(false);
            areThereAnyTags.setRequired(false);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false);
            contractLanguageForGrievanceReporting.setRequired(false); 

            // Show/Hide fields for ""Custom - Enhancement"" option
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true);
            isThisReusable.setRequired(false); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);

            sensitiveInformation.setVisible(true);
            sensitiveInformation.setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
            frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
            modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(true);
            standardAddOns.setVisible(false);//added JIRA-9551
            break;

        case ""New"":
            // Show/Hide fields for ""New"" option
            standardDataExtractType.setVisible(false);
            standardDataExtractType.setRequired(false);
            areThereAnyTags.setVisible(false);
            areThereAnyTags.setRequired(false);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false);
            contractLanguageForGrievanceReporting.setRequired(false); 
               
            // Show/Hide fields for ""New"" option
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(false);
            isThisReusable.setRequired(false); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true);
            sensitiveInformation.setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
            frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
            modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            standardAddOns.setVisible(false);//added JIRA-9551
            break;

        case ""Enhancement"":
            // Show/Hide fields for ""Standar - New"" option
            standardDataExtractType.setVisible(false);
            standardDataExtractType.setRequired(false);
            areThereAnyTags.setVisible(false);
            areThereAnyTags.setRequired(false);

            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false);
            contractLanguageForGrievanceReporting.setRequired(false); 

            // Show/Hide fields for ""Enhancement"" option
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true);
            isThisReusable.setRequired(false); 
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true);
            sensitiveInformation.setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true);
            frequency.setRequired(true);
            modeOfDelivery.setVisible(true);
            modeOfDelivery.setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            standardAddOns.setVisible(false);//added JIRA-9551
            break;
        default:
            standardAddOns.setVisible(false);//added JIRA-9551
            break;
    }

}","/*
Creator: Eduardo Rojo
Purpose: CRUS project - Client Reporting - USGH - Hide fields based on New or Enhancement
Change log: Rewritten for Jira Cloud compatibility
*/

// Check if New or Enhancement field has changed
if (getChangeField().getName() === ""New or Enhancement"") {
    console.log(""Behaviours CRUS Project – New or Enhancement started."");

    const newOrEnhancementValue = getChangeField().getValue().value;
    console.log(""New or Enhancement:"", newOrEnhancementValue);

    // Get field references with proper custom field IDs
    const standardDataExtractType = getFieldById(""customfield_10082""); // Field (Standard Data Extract Type)
    const areThereAnyTags = getFieldById(""customfield_10085""); // Field (Are there any tags?)
    const clientName = getFieldById(""customfield_10086""); // Field (Client Name)
    const clientLaunchDate = getFieldById(""customfield_10087""); // Field (Client Launch Date)
    const contractLanguageForGrievanceReporting = getFieldById(""customfield_10088""); // Field (Contract Language for Grievance Reporting)
    const clientLevel = getFieldById(""customfield_10089""); // Field (Client Level)
    const businessRequirements = getFieldById(""customfield_10084""); // Field (Business Requirements)
    const isThisReusable = getFieldById(""customfield_10090""); // Field (Is this Reusable?)
    const reportingTeamOwner = getFieldById(""customfield_10091""); // Field (Reporting Team Owner)
    const approver = getFieldById(""customfield_10092""); // Field (Approver)
    const audience = getFieldById(""customfield_10093""); // Field (Audience (SL))
    const audienceDetails = getFieldById(""customfield_10094""); // Field (Audience Details)
    const contractualObligation = getFieldById(""customfield_10095""); // Field (Contractual Obligation)
    const sOWorBRDRequired = getFieldById(""customfield_10096""); // Field (SOW or BRD required?)
    const sensitiveInformation = getFieldById(""customfield_10097""); // Field (Sensitive Information)
    const fileTypePGPEncryption = getFieldById(""customfield_10098""); // Field (File Type PGP Encryption)
    const fileTypeExtension = getFieldById(""customfield_10099""); // Field (File Type Extension)
    const fileTypeDelimiter = getFieldById(""customfield_10100""); // Field (File Type Delimiter)
    const extensionDetails = getFieldById(""customfield_10101""); // Field (Extension Details)
    const frequency = getFieldById(""customfield_10102""); // Field (Frequency)
    const modeOfDelivery = getFieldById(""customfield_10103""); // Field (Mode of Delivery)
    const modeOfDeliveryVendor = getFieldById(""customfield_10104""); // Field (SFTP - Client/Vendor Details)
    const modeOfDeliveryEmail = getFieldById(""customfield_10105""); // Field (Email Delivery - Provide Email)
    const priorityScore = getFieldById(""customfield_10106""); // Field (Priority Score)
    const standardAddOns = getFieldById(""customfield_10107""); // Field (Standard Add Ons)

    // Reset fields
    standardDataExtractType.setVisible(false).setRequired(false);

    switch(newOrEnhancementValue) {
        case ""Standard - New"":
            standardDataExtractType.setVisible(true).setRequired(true);
            areThereAnyTags.setVisible(true).setRequired(true);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false).setRequired(false);
            clientLevel.setRequired(false);
            businessRequirements.setRequired(false);
            isThisReusable.setVisible(false).setRequired(false);
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true).setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true).setRequired(true);
            modeOfDelivery.setVisible(true).setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(false);
            standardAddOns.setVisible(true);
            break;

        case ""Standard - Enhancement"":
            standardDataExtractType.setVisible(true).setRequired(true);
            areThereAnyTags.setVisible(true).setRequired(true);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false).setRequired(false);
            clientLevel.setRequired(false);
            businessRequirements.setRequired(false);
            isThisReusable.setVisible(false).setRequired(false);
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true).setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true).setRequired(true);
            modeOfDelivery.setVisible(true).setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(false);
            standardAddOns.setVisible(true);
            break;

        case ""Custom - New"":
            standardDataExtractType.setVisible(false).setRequired(false);
            areThereAnyTags.setVisible(false).setRequired(false);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false).setRequired(false);
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true).setRequired(true);
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true).setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true).setRequired(true);
            modeOfDelivery.setVisible(true).setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(true);
            standardAddOns.setVisible(false);
            break;

        case ""Custom - Enhancement"":
            standardDataExtractType.setVisible(false).setRequired(false);
            areThereAnyTags.setVisible(false).setRequired(false);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false).setRequired(false);
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true).setRequired(false);
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true).setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true).setRequired(true);
            modeOfDelivery.setVisible(true).setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            priorityScore.setVisible(true);
            standardAddOns.setVisible(false);
            break;

        case ""New"":
            standardDataExtractType.setVisible(false).setRequired(false);
            areThereAnyTags.setVisible(false).setRequired(false);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false).setRequired(false);
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(false).setRequired(false);
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true).setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true).setRequired(true);
            modeOfDelivery.setVisible(true).setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            standardAddOns.setVisible(false);
            break;

        case ""Enhancement"":
            standardDataExtractType.setVisible(false).setRequired(false);
            areThereAnyTags.setVisible(false).setRequired(false);
            clientLaunchDate.setRequired(false);
            contractLanguageForGrievanceReporting.setVisible(false).setRequired(false);
            clientLevel.setRequired(true);
            businessRequirements.setRequired(true);
            isThisReusable.setVisible(true).setRequired(false);
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sensitiveInformation.setVisible(true).setRequired(true);
            fileTypePGPEncryption.setVisible(true);
            fileTypeExtension.setVisible(true);
            fileTypeDelimiter.setVisible(true);
            extensionDetails.setVisible(false);
            frequency.setVisible(true).setRequired(true);
            modeOfDelivery.setVisible(true).setRequired(true);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryEmail.setVisible(false);
            standardAddOns.setVisible(false);
            break;

        default:
            standardAddOns.setVisible(false);
            break;
    }

    console.log(""Behaviours CRUS Project – New or Enhancement completed."");
}"
5,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
// com.atlassian.jira.issue.customfields.option.Options;
/**import com.atlassian.jira.issue.customfields.option.Options

 * Eduardo Rojo
 * CRUS project - Client Reporting - USGH
 * Issue Type: Story
 * Hide fields based on ""Standard Data Extract Type""
 * Log: 5/3/2023 Eduardo Rojo (JIRA-5208)
 *      6/5/2024 Eduardo Rojo (JIRA-9551)
 */

console.log(""Behaviours CRUS Project – Standard Data Extract Type started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager()
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Standard Data Extract Type"" field has changed
let standardDataExtractType = getFieldById(getFieldChanged());

// This gets fields by name
let membershipReport = getFieldById(""Membership Report"");
let bloodGlucoseReport = getFieldById(""Blood Glucose Report"");
let bloodPressureReport = getFieldById(""Blood Pressure Report"");
let weightManagementReport = getFieldById(""Weight Management Report"");
let alertsReport = getFieldById(""Alerts Report"");
let coachingReport = getFieldById(""Coaching Report"");
let grievanceReport = getFieldById(""Grievance Report"");
let incentiveReport = getFieldById(""Incentive Report"");
let meterMessageReport = getFieldById(""Meter Message Report"");
let engagementReport = getFieldById(""Engagement Report"");
let ineligibleMembersReport = getFieldById(""Ineligible Members Report""); 
let mentalHealthEventsReport = getFieldById(""Mental Health Events Report""); //(JIRA-5208) Custom field name updated from Behavioral Health Events Report to Mental Health Events Report
let marketingOptOutReport = getFieldById(""Marketing Opt Out Report"");
let clientName = getFieldById(""Client Name"");
let clientLaunchDate = getFieldById(""Client Launch Date"");
let contractLanguageForGrievanceReporting = getFieldById(""Contract Language for Grievance Reporting"");
let eMSCaseDetailExtractReport = getFieldById(""EMS Case Detail Extract Report""); // added (JIRA-5208)
let estimatedA1cReport = getFieldById(""Estimated A1c Report""); // added (JIRA-5208)
let clinicalOutcomesMemberReport = getFieldById(""Clinical Outcomes Member Report""); // ER added 9/27/2023 - JIRA-8466
let detailedInvoicesReport = getFieldById(""Detailed Invoices Report"");//added JIRA-9551
let registrationReport = getFieldById(""Registration Report"");//added JIRA-9551

// This gets the value of ""Standard Report Type (SL)""
let standardReportTypeOption = standardDataExtractType.getValue() as String

console.log(""Standard Data Extract Type: "" + standardReportTypeOption);
// Save true/false if standardReportTypeOption == (value selected)
let mentalHealthEventsReportNew = standardReportTypeOption == ""Mental Health Events"" //updated (JIRA-5208)
let bloodGlucoseReportNew = standardReportTypeOption == ""Blood Glucose""
let alertsReportNew = standardReportTypeOption == ""Blood Glucose Alerts""
let bloodPressureReportNew = standardReportTypeOption == ""Blood Pressure""
let coachingReportNew = standardReportTypeOption == ""Coaching""
let engagementReportNew = standardReportTypeOption == ""Engagement""
let grievanceReportNew = standardReportTypeOption == ""Grievances""
let incentiveReportNew = standardReportTypeOption == ""Incentive Report""
let ineligibleMembersReportNew = standardReportTypeOption == ""Ineligible Members""
let marketingOptOutReportNew = standardReportTypeOption == ""Marketing Opt Out""
let membershipReportNew = standardReportTypeOption == ""Membership""
let meterMessageReportNew = standardReportTypeOption == ""Meter Message""
let weightManagementReportNew = standardReportTypeOption == ""Weight Management""
let eMSCaseDetailExtractReportNew = standardReportTypeOption == ""EMS Case Detail Extract""
let estimatedA1cReportNew = standardReportTypeOption == ""Estimated A1c (Health Plans Only)""
let clinicalOutcomesMemberReportNew = standardReportTypeOption == ""Quarterly Clinical Outcomes Member File""; // ER added 9/27/2023 - JIRA-8466
let detailedBillingReportNew = standardReportTypeOption == ""Detailed Invoices"" //added JIRA-9551
let registrationReportNew = standardReportTypeOption == ""Registration"" //added JIRA-9551

// Preperation code for hiding ""Standard Report Type"" field
let standardDataExtractTypeCF = customFieldManager.getCustomFieldObject(standardDataExtractType.getFieldId())
let config = standardDataExtractTypeCF.getRelevantConfig(getIssueContext())
let options = optionsManager.getOptions(config) as Options

// Show/Hide fields
membershipReport.setVisible(!!membershipReportNew);
bloodGlucoseReport.setVisible(!!bloodGlucoseReportNew);
bloodPressureReport.setVisible(!!bloodPressureReportNew);
weightManagementReport.setVisible(!!weightManagementReportNew);
alertsReport.setVisible(!!alertsReportNew);
coachingReport.setVisible(!!coachingReportNew);
grievanceReport.setVisible(!!grievanceReportNew);
incentiveReport.setVisible(!!incentiveReportNew);
meterMessageReport.setVisible(!!meterMessageReportNew);
engagementReport.setVisible(!!engagementReportNew);
ineligibleMembersReport.setVisible(!!ineligibleMembersReportNew);
mentalHealthEventsReport.setVisible(!!mentalHealthEventsReportNew);//updated (JIRA-5208) 
marketingOptOutReport.setVisible(!!marketingOptOutReportNew);
//clientName.setVisible(!!grievanceReportNew); //updated (JIRA-5208)
//clientLaunchDate.setVisible(!!grievanceReportNew); //updated (JIRA-5208)
contractLanguageForGrievanceReporting.setVisible(!!grievanceReportNew);
eMSCaseDetailExtractReport.setVisible(!!eMSCaseDetailExtractReportNew); // added (JIRA-5208)
estimatedA1cReport.setVisible(!!estimatedA1cReportNew); // added (JIRA-5208)
clinicalOutcomesMemberReport.setVisible(!!clinicalOutcomesMemberReportNew); // ER added 9/27/2023 - JIRA-8466
detailedInvoicesReport.setVisible(!!detailedBillingReportNew); //added JIRA-9551
registrationReport.setVisible(!!registrationReportNew); //added JIRA-9551
console.log(""Behaviours CRUS Project – Standard Data Extract Type completed."") ","let data = null

// This listens if the ""Standard Data Extract Type"" field has changed
let standardDataExtractType = getFieldById(getFieldChanged());

if (getChangeField().getName() == ""Standard Data Extract Type"") {
    data = getChangeField().getValue().value
}


if (data != null) {

    let membershipReport = getFieldById(""customfield_10089""); // Field (Membership Report)
    let bloodGlucoseReport = getFieldById(""customfield_10089""); // Field (Blood Glucose Report)
    let bloodPressureReport = getFieldById(""customfield_10089""); // Field (Blood Pressure Report)
    let weightManagementReport = getFieldById(""customfield_10089""); // Field (Weight Management Report)
    let alertsReport = getFieldById(""customfield_10089""); // Field (Alerts Report)
    let coachingReport = getFieldById(""customfield_10089""); // Field (Coaching Report)
    let grievanceReport = getFieldById(""customfield_10089""); // Field (Grievance Report)
    let incentiveReport = getFieldById(""customfield_10089""); // Field (Incentive Report)
    let meterMessageReport = getFieldById(""customfield_10089""); // Field (Meter Message Report)
    let engagementReport = getFieldById(""customfield_10089""); // Field (Engagement Report)
    let ineligibleMembersReport = getFieldById(""customfield_10089""); // Field (Ineligible Members Report)
    let mentalHealthEventsReport = getFieldById(""customfield_10089""); // Field (Mental Health Events Report)
    let marketingOptOutReport = getFieldById(""customfield_10089""); // Field (Marketing Opt Out Report)
    let clientName = getFieldById(""customfield_10089""); // Field (Client Name)
    let clientLaunchDate = getFieldById(""customfield_10089""); // Field (Client Launch Date)
    let contractLanguageForGrievanceReporting = getFieldById(""customfield_10089""); // Field (Contract Language for Grievance Reporting)
    let eMSCaseDetailExtractReport = getFieldById(""customfield_10089""); // Field (EMS Case Detail Extract Report)
    let estimatedA1cReport = getFieldById(""customfield_10089""); // Field (Estimated A1c Report)
    let clinicalOutcomesMemberReport = getFieldById(""customfield_10089""); // Field (Clinical Outcomes Member Report)
    let detailedInvoicesReport = getFieldById(""customfield_10089""); // Field (Detailed Invoices Report)
    let registrationReport = getFieldById(""customfield_10089""); // Field (Registration Report)

    // This gets the value of ""Standard Report Type (SL)""
    let standardReportTypeOption = standardDataExtractType.getValue() as String

    // Save true/false if standardReportTypeOption == (value selected)
    let mentalHealthEventsReportNew = standardReportTypeOption == ""Mental Health Events"" //updated (JIRA-5208)
    let bloodGlucoseReportNew = standardReportTypeOption == ""Blood Glucose""
    let alertsReportNew = standardReportTypeOption == ""Blood Glucose Alerts""
    let bloodPressureReportNew = standardReportTypeOption == ""Blood Pressure""
    let coachingReportNew = standardReportTypeOption == ""Coaching""
    let engagementReportNew = standardReportTypeOption == ""Engagement""
    let grievanceReportNew = standardReportTypeOption == ""Grievances""
    let incentiveReportNew = standardReportTypeOption == ""Incentive Report""
    let ineligibleMembersReportNew = standardReportTypeOption == ""Ineligible Members""
    let marketingOptOutReportNew = standardReportTypeOption == ""Marketing Opt Out""
    let membershipReportNew = standardReportTypeOption == ""Membership""
    let meterMessageReportNew = standardReportTypeOption == ""Meter Message""
    let weightManagementReportNew = standardReportTypeOption == ""Weight Management""
    let eMSCaseDetailExtractReportNew = standardReportTypeOption == ""EMS Case Detail Extract""
    let estimatedA1cReportNew = standardReportTypeOption == ""Estimated A1c (Health Plans Only)""
    let clinicalOutcomesMemberReportNew = standardReportTypeOption == ""Quarterly Clinical Outcomes Member File""; // ER added 9/27/2023 - JIRA-8466
    let detailedBillingReportNew = standardReportTypeOption == ""Detailed Invoices"" //added JIRA-9551
    let registrationReportNew = standardReportTypeOption == ""Registration"" //added JIRA-9551

    // Segment does nothing - this is from DC, added a comment in case something strangely fails in the future.
    // Preperation code for hiding ""Standard Report Type"" field
    // let standardDataExtractTypeCF = customFieldManager.getCustomFieldObject(standardDataExtractType.getFieldId())
    // let config = standardDataExtractTypeCF.getRelevantConfig(getIssueContext())
    // let options = optionsManager.getOptions(config) as Options

    // Show/Hide fields
    membershipReport.setVisible(membershipReportNew);
    bloodGlucoseReport.setVisible(bloodGlucoseReportNew);
    bloodPressureReport.setVisible(bloodPressureReportNew);
    weightManagementReport.setVisible(weightManagementReportNew);
    alertsReport.setVisible(alertsReportNew);
    coachingReport.setVisible(coachingReportNew);
    grievanceReport.setVisible(grievanceReportNew);
    incentiveReport.setVisible(incentiveReportNew);
    meterMessageReport.setVisible(meterMessageReportNew);
    engagementReport.setVisible(engagementReportNew);
    ineligibleMembersReport.setVisible(ineligibleMembersReportNew);
    mentalHealthEventsReport.setVisible(mentalHealthEventsReportNew);//updated (JIRA-5208) 
    marketingOptOutReport.setVisible(marketingOptOutReportNew);
    contractLanguageForGrievanceReporting.setVisible(grievanceReportNew);
    eMSCaseDetailExtractReport.setVisible(eMSCaseDetailExtractReportNew); // added (JIRA-5208)
    estimatedA1cReport.setVisible(estimatedA1cReportNew); // added (JIRA-5208)
    clinicalOutcomesMemberReport.setVisible(clinicalOutcomesMemberReportNew); // ER added 9/27/2023 - JIRA-8466
    detailedInvoicesReport.setVisible(detailedBillingReportNew); //added JIRA-9551
    registrationReport.setVisible(registrationReportNew); //added JIRA-9551

}

"
6,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * CRUS project - Client Reporting - USGH
 * Issue Type: Story
 * Hide fields based on ""Are there any tags?""
 */

console.log(""Behaviours CRUS Project – Are there any tags? started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager()
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Are there any tags?"" field has changed
let areThereAnyTags = getFieldById(getFieldChanged());

// This saves the custom field ""Tags"" under the name tags
let tags = getFieldById(""Tags"");

// This gets the value of ""Are there any tags?""
let areThereAnyTagsOption = areThereAnyTags.getValue() as String

console.log(""Are there any tags?: "" + areThereAnyTagsOption);
// Show/Hide and set ""tags"" as Required 
let tagsNew = areThereAnyTagsOption == ""Yes""
tags.setVisible(!!tagsNew);
tags.setRequired(tagsNew);

// Show Help Text under ""Tags"" field
if (tagsNew){
        tags.setHelpText(""Please provide tag, unique identifier, client specific identifier (separate each tag by comma)."");    
}
else{
    tags.setHelpText("""")
}
console.log(""Behaviours CRUS Project – Are there any tags? completed."") ","let data = null

if (getChangeField().getName() == ""Are there any tags?"" && getChangeField().getValue().value == ""Yes"" ) {
    // This saves the custom field ""Tags"" under the name tags
    getFieldById(""customfield_10089"").setDescription(""Please provide tag, unique identifier, client specific identifier (separate each tag by comma).""); // Field (Tags) 
    getFieldById(""customfield_10089"").setRequired(true); // Field (Tags)
    getFieldById(""customfield_10089"").setVisible(true); // Field (Tags)
}"
7,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * CRUS project - Client Reporting - USGH
 * Issue Type: Story
 * Hide fields based on ""File Type Extension""
 */

console.log(""Behaviours CRUS Project – File Type Extension started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager()
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""File Type Extension"" field has changed
let fileTypeExtension = getFieldById(getFieldChanged());

// This saves the custom field ""Extension Details"" under the name extensionDetails
let extensionDetails = getFieldById(""Extension Details"");

// This gets the value of ""File Type Extension""
let fileTypeExtensionOption = fileTypeExtension.getValue() as String

console.log(""File Type Extension: "" + fileTypeExtensionOption);
// Saves true/false if the value of the option is ""other""
let other = fileTypeExtensionOption == ""other""

// Show/Require ""Extension Details"" field
extensionDetails.setVisible(!!other);
extensionDetails.setRequired(other);
console.log(""Behaviours CRUS Project – File Type Extension completed."") ","let data = null

if (getChangeField().getName() == ""File Type Extension"") {
    data = getChangeField().getValue().value

    // This listens if the ""File Type Extension"" field has changed
    let fileTypeExtension = data

    // This saves the custom field ""Extension Details"" under the name extensionDetails
    let extensionDetails = getFieldById(""customfield_10080""); // Field (Extension Details)

    // This gets the value of ""File Type Extension""
    let fileTypeExtensionOption = fileTypeExtension

    // Saves true/false if the value of the option is ""other""
    let other = fileTypeExtensionOption == ""other""

    // Show/Require ""Extension Details"" field
    extensionDetails.setVisible(other);
    extensionDetails.setRequired(other);


}



"
8,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * CRUS project - Client Reporting - USGH
 * Issue Type: Story
 * Hide fields based on ""Mode of Delivery""
 * Log: 5/3/2023 Eduardo Rojo (JIRA-5208)
 */

console.log(""Behaviours CRUS Project – Mode of Delivery started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager()
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Mode of Delivery"" field has changed
let modeOfDelivery = getFieldById(getFieldChanged());

// This saves the custom field ""SFTP - Client/Vendor Details"" and ""Email Delivery - Provide Email""
let vendorDetails = getFieldById(""SFTP Host and Folder"");
let emailDelivery = getFieldById(""Email Delivery - Provide Email"");
let boxLocation = getFieldById(""Box Location""); //added 5/3/2023 Eduardo Rojo (JIRA-5208) 

// This gets the value of ""Mode of Delivery"" field
let modeOfDeliveryOption = modeOfDelivery.getValue() as String

console.log(""Mode of Delivery: "" + modeOfDeliveryOption);
// Saves true/false if the ""Mode of Delivery"" option is match
let vendorDetailsNew = modeOfDeliveryOption == ""SFTP""
//let vendorDetailsExisting = modeOfDeliveryOption == ""SFTP (Existing Connection)""
let emailDeliveryNew = modeOfDeliveryOption == ""Email Delivery – For reports not containing PHI or PII""

let boxLocationNew = modeOfDeliveryOption == ""Box"" //added 5/3/2023 Eduardo Rojo (JIRA-5208) 

// Shows ""SFTP - Client/Vendor Details"" field when the option is ""SFTP""
if(vendorDetailsNew){
    vendorDetails.setVisible(true);
        vendorDetails.setRequired(true);
}
else{
    vendorDetails.setVisible(false);
        vendorDetails.setRequired(false);
}

// // Shows ""Email Delivery - Provide Email"" field when the option is ""Email Delivery – For reports not containing PHI or PII""
emailDelivery.setVisible(!!emailDeliveryNew);
emailDelivery.setRequired(emailDeliveryNew);

boxLocation.setVisible(!!boxLocationNew); //added 5/3/2023 Eduardo Rojo (JIRA-5208)
boxLocation.setRequired(boxLocationNew); //added 5/3/2023 Eduardo Rojo (JIRA-5208)
console.log(""Behaviours CRUS Project – Mode of Delivery completed."")","

if (getChangeField().getName() == ""Mode of Delivery"" ) {    

        // This listens if the ""Mode of Delivery"" field has changed
    let modeOfDelivery = getFieldById(""customfield_10090""); // Field (Mode of Delivery) 

    // This saves the custom field ""SFTP - Client/Vendor Details"" and ""Email Delivery - Provide Email""
    let vendorDetails = getFieldById(""customfield_10090""); // Field (SFTP Host and Folder)
    let emailDelivery = getFieldById(""customfield_10090""); // Field (Email Delivery - Provide Email)
    let boxLocation = getFieldById(""customfield_10090""); // Field (Box Location)

    // This gets the value of ""Mode of Delivery"" field
    let modeOfDeliveryOption = modeOfDelivery.getValue().value

    // Saves true/false if the ""Mode of Delivery"" option is match
    let vendorDetailsNew = modeOfDeliveryOption == ""SFTP""

    //let vendorDetailsExisting = modeOfDeliveryOption == ""SFTP (Existing Connection)""
    let emailDeliveryNew = modeOfDeliveryOption == ""Email Delivery – For reports not containing PHI or PII""
    let boxLocationNew = modeOfDeliveryOption == ""Box"" // added 5/3/2023 Eduardo Rojo (JIRA-5208) 

    // Shows ""SFTP - Client/Vendor Details"" field when the option is ""SFTP""
    if(vendorDetailsNew){
        vendorDetails.setVisible(true);
        vendorDetails.setRequired(true);
    } else {
        vendorDetails.setVisible(false);
        vendorDetails.setRequired(false);
    }

    // Shows ""Email Delivery - Provide Email"" field when the option is ""Email Delivery – For reports not containing PHI or PII""
    emailDelivery.setVisible(emailDeliveryNew);
    emailDelivery.setRequired(emailDeliveryNew);

    boxLocation.setVisible(boxLocationNew);  // Added 5/3/2023 Eduardo Rojo (JIRA-5208)
    boxLocation.setRequired(boxLocationNew); // Added 5/3/2023 Eduardo Rojo (JIRA-5208)

}

"
9,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * CRUS project - Client Reporting - USGH
 * Issue Type: Story
 * Hide fields based on ""Data Sharing Agreement""
 * Log: 4/18/2023 Eduardo Rojo (JIRA-5208) 
 */

console.log(""Behaviours CRUS Project – Data Sharing Agreement started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager()
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Contractual Obligation"" field has changed
let dataSharingAgreement = getFieldById(getFieldChanged());

// This saves the custom field ""DSA Link"" under the name tags
let dsaLink = getFieldById(""DSA Link"");

// This gets the value of ""Contractual Obligation""""
let dataSharingAgreementOption = dataSharingAgreement.getValue() as String

console.log(""Data Sharing Agreement: "" + dataSharingAgreementOption);
// Show/Hide and set ""DSA Link"" as Required 
let dsaLinkNew = dataSharingAgreementOption == ""Yes""
dsaLink.setVisible(!!dsaLinkNew);
// dsaLink.setRequired(dsaLinkNew);
console.log(""Behaviours CRUS Project – Data Sharing Agreement completed."");","if (getChangeField().getName() == ""Contractual Obligation"") {

    // This listens if the ""Contractual Obligation"" field has changed
    let dataSharingAgreement = getFieldById(""customfield_10090""); // Field (Contractual Obligation)

    // This saves the custom field ""DSA Link"" under the name tags
    let dsaLink = getFieldById(""customfield_10090""); // Field (DSA Link)

    // This gets the value of ""Contractual Obligation""""
    let dataSharingAgreementOption = dataSharingAgreement.getValue().value

    // Show/Hide and set ""DSA Link"" as Required 
    let dsaLinkNew = dataSharingAgreementOption == ""Yes""

    dsaLink.setVisible(dsaLinkNew);

}"
10,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
// com.atlassian.jira.issue.customfields.option.Options
/**import org.apache.jasper.Options

 * Eduardo Rojo
 * CRUS project - Client Reporting - USGH
 * Issue Type: Story
 * Hide fields based on ""Request Type"" Question and set values for Report Type
 * Log: 3/8/2023 Eduardo Rojo (JIRA-5208)
 * ER 2/2024 JIRA-8908
 */
console.log(""Behaviours CRUS Project – Request Type started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Request Type"" field has changed
let requestType = getFieldById(getFieldChanged());

// This saves custom field's ids to manage its behaviors
let newOrEnhancement = getFieldById(""New or Enhancement"");
let standardDataExtractType = getFieldById(""Standard Data Extract Type"");
let reportName = getFieldById(""Report Name"");
let description = getFieldById(""Description"");
let clientLevel = getFieldById(""Client Level"");
let businessRequirements = getFieldById(""Business Requirements"");
let areThereAnyTags = getFieldById(""Are there any tags?"");
let clientName = getFieldById(""Client Name"");
let clientLaunchDate = getFieldById(""Client Launch Date"");
let contractLanguageForGrievanceReporting = getFieldById(""Contract Language for Grievance Reporting"");
let isThisReusable = getFieldById(""Is this Reusable?"");
let reportingTeamOwner = getFieldById(""Reporting Team Owner"");
let approver = getFieldById(""Approver"");
let audience = getFieldById(""Audience (SL)"");
let audienceDetails = getFieldById(""Audience Details"");
let contractualObligation = getFieldById(""Contractual Obligation"");
let sOWorBRDRequired = getFieldById(""SOW or BRD required?"");
let sensitiveInformation = getFieldById(""Sensitive Information"");
let dataSharingAgreement = getFieldById(""Data Sharing Agreement"");
let fileTypePGPEncryption = getFieldById(""File Type PGP Encryption"");
let fileTypeExtension = getFieldById(""File Type Extension"");
let fileTypeDelimiter = getFieldById(""File Type Delimiter"");
let extensionDetails = getFieldById(""Extension Details"");
let testFileRequired = getFieldById(""Test file Required?"");
let frequency = getFieldById(""Frequency"");
let modeOfDelivery = getFieldById(""Mode of Delivery"");
let modeOfDeliveryVendor = getFieldById(""SFTP Host and Folder"");
let modeOfDeliveryEmail = getFieldById(""Email Delivery - Provide Email""); 
let priorityScore = getFieldById(""Priority Score"");
// Start (JIRA-5208)
let purpose = getFieldById(""Purpose"");
let clientType = getFieldById(""Client Type"");
let services = getFieldById(""Services"");
let products = getFieldById(""Products (TF)"");
let groupName = getFieldById(""Group Name"");
let recipientCompany = getFieldById(""Recipient Company"");
let recipientType = getFieldById(""Recipient Type"");
let boxLocarion = getFieldById(""Box Location"");
let dataDeliveryApprovalReason = getFieldById(""Data Delivery Approval Reason"");
let privacyApproval = getFieldById(""Privacy Approval"");
let privacyApprovalComments = getFieldById(""Privacy Approval Comments"");
let informationSecurityApproval = getFieldById(""Information Security Approval"");
let informationSecurityApprovalComments = getFieldById(""Information Security Approval Comments"");
let clinicalAnalyticsApproval = getFieldById(""Clinical Analytics Approval"");
let clinicalAnalyticsApprovalComments = getFieldById(""Clinical Analytics Approval Comments"");
let dataScienceApproval = getFieldById(""Data Science Approval"");
let dataScienceApprovalComments = getFieldById(""Data Science Approval Comments"");
let pricing = getFieldById(""Pricing (MC)"");
let levelOfEffort = getFieldById(""Level of Effort"");
// End (JIRA-5208)

// Start - ER 2/2024 JIRA-8908
let noOfEligibleLivesForTheClient = getFieldById(""No. of Eligible Lives for the Client"");
let strategicValueOfRequest = getFieldById(""Strategic Value of Request"");
let isThereAWorkaroundToGetYourDataOrSolution = getFieldById(""Is there a workaround to get your data or solution?"");
let escalation = getFieldById(""Escalation"");
// End ER 2/2024 JIRA-8908

// This gets the value of ""Request Type"" as String
let requestTypeOption = requestType.getValue() as String

console.log(""Request Type: "" + requestTypeOption);
// This get the values of ""Report Type"" to set different options 
let cfField = customFieldManager.getCustomFieldObject(newOrEnhancement.getFieldId())
let cfConfig = cfField.getRelevantConfig(getIssueContext())
let cfOptions = optionsManager.getOptions(cfConfig)


switch(requestTypeOption){
    case ""Data Extract | Member/Visit/Event level"":
            // Showing/Requiring Report Type
            newOrEnhancement.setVisible(true);
            newOrEnhancement.setRequired(true);
            // Showing only some values of the original list
                let cfA = cfOptions.findAll 
            { it.getValue() in [""----"",""Standard - New"",""Standard - Enhancement"",""Custom - New"",""Custom - Enhancement""] }.collectEntries 
            { [ (it.optionId.toString()) : it.getValue() ] } as Map
                newOrEnhancement.setOptionsVisibility(cfA);
            console.log(""Custom Option List: "" + cfA);
            clientLevel.setRequired(false);
            reportName.setVisible(false);
                reportName.setRequired(false);

                //Start (JIRA-5208)
                dataSharingAgreement.setVisible(true);
                purpose.setVisible(true);
                purpose.setRequired(true);
                clientType.setVisible(true);
                clientType.setRequired(true);
                services.setVisible(true);
                services.setRequired(true);
                products.setVisible(true);
                clientName.setVisible(true);
                clientName.setRequired(true);
                groupName.setVisible(true);
                recipientCompany.setVisible(true);
                recipientCompany.setRequired(true);
                recipientType.setVisible(true);
                recipientType.setRequired(true);
                dataDeliveryApprovalReason.setVisible(true);
                privacyApproval.setVisible(true);
                privacyApprovalComments.setVisible(true); 
                informationSecurityApproval.setVisible(true); 
                informationSecurityApprovalComments.setVisible(true); 
                clinicalAnalyticsApproval.setVisible(true);
                clinicalAnalyticsApprovalComments.setVisible(true);
                pricing.setVisible(true);
                levelOfEffort.setVisible(true);
                dataScienceApproval.setVisible(true);
                dataScienceApprovalComments.setVisible(true);
                clientLaunchDate.setVisible(true);

                modeOfDelivery.setVisible(true);
                modeOfDelivery.setRequired(true);
                // End (JIRA-5208)

                // Start ER 2/2024 JIRA-8908
                noOfEligibleLivesForTheClient.setVisible(false);
                noOfEligibleLivesForTheClient.setRequired(false);
                isThisReusable.setVisible(true);
            //isThisReusable.setRequired(false);
                strategicValueOfRequest.setVisible(false);
                strategicValueOfRequest.setRequired(false);
                isThereAWorkaroundToGetYourDataOrSolution.setVisible(false);
                isThereAWorkaroundToGetYourDataOrSolution.setRequired(false);
                audience.setVisible(true);
                //audience.setRequired(false);
            audienceDetails.setVisible(true);
                //audienceDetails.setRequired(false);
                contractualObligation.setVisible(true);
                //contractualObligation.setRequired(false);
                escalation.setVisible(false);
                escalation.setRequired(false);
                priorityScore.setVisible(false);
                // End ER 2/2024 JIRA-8908

            break;
    case ""Client Report Feedback"":
            // Showing optional/required fields
            newOrEnhancement.setVisible(false);
            newOrEnhancement.setRequired(false);
            clientLevel.setRequired(false);
            isThisReusable.setVisible(true);
            isThisReusable.setRequired(true);
            reportingTeamOwner.setVisible(false);
            approver.setVisible(false);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(false);
            sOWorBRDRequired.setVisible(false);
            sOWorBRDRequired.setRequired(false);
            sensitiveInformation.setVisible(false);
            sensitiveInformation.setRequired(false);
            dataSharingAgreement.setVisible(false);
            // dataSharingAgreement.setRequired(false);
            fileTypePGPEncryption.setVisible(false);
            fileTypeExtension.setVisible(false);
            fileTypeDelimiter.setVisible(false);
            extensionDetails.setVisible(false);
            testFileRequired.setVisible(false);
            testFileRequired.setRequired(false);
            frequency.setVisible(false);
            frequency.setRequired(false);
            modeOfDelivery.setVisible(false);
            modeOfDelivery.setRequired(false);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryVendor.setRequired(false);
            modeOfDeliveryEmail.setVisible(false);
            modeOfDeliveryEmail.setRequired(false);
            reportName.setVisible(true);
                reportName.setRequired(false);

                //Start (JIRA-5208)
                purpose.setVisible(false);
                purpose.setRequired(false);
                clientType.setVisible(false);
                clientType.setRequired(false);
                services.setVisible(false);
                services.setRequired(false);
                products.setVisible(false);
                clientName.setVisible(false);
                clientName.setRequired(false);
                groupName.setVisible(false);
                recipientCompany.setVisible(false);
                recipientCompany.setRequired(false);
                recipientType.setVisible(false);
                recipientType.setRequired(false);
                dataDeliveryApprovalReason.setVisible(false);
                privacyApproval.setVisible(false);
                privacyApprovalComments.setVisible(false); 
                informationSecurityApproval.setVisible(false); 
                informationSecurityApprovalComments.setVisible(false); 
                clinicalAnalyticsApproval.setVisible(false);
                clinicalAnalyticsApprovalComments.setVisible(false);
                pricing.setVisible(false);
                levelOfEffort.setVisible(false);
                dataScienceApproval.setVisible(false);
                dataScienceApprovalComments.setVisible(false);
                clientLaunchDate.setVisible(false);
                // End (JIRA-5208)

                // Start ER 2/2024 JIRA-8908
                noOfEligibleLivesForTheClient.setVisible(true);
                noOfEligibleLivesForTheClient.setRequired(true);
                isThisReusable.setVisible(true);
            isThisReusable.setRequired(true);
                strategicValueOfRequest.setVisible(true);
                strategicValueOfRequest.setRequired(true);
                isThereAWorkaroundToGetYourDataOrSolution.setVisible(true);
                isThereAWorkaroundToGetYourDataOrSolution.setRequired(true);
                audience.setVisible(true);
                audience.setRequired(true);
            audienceDetails.setVisible(true);
                audienceDetails.setRequired(true);
                contractualObligation.setVisible(true);
                contractualObligation.setRequired(true);
                escalation.setVisible(true);
                escalation.setRequired(true);
                priorityScore.setVisible(true);
                // End ER 2/2024 JIRA-8908
            break;
    case ""Custom Client Report | Aggregated/Summary"":
            // Showing/Requiring Report Type
            newOrEnhancement.setVisible(true);
            newOrEnhancement.setRequired(true);
            // Showing only some values of the original list
            let cfA = cfOptions.findAll 
            { it.getValue() in [""----"",""New"",""Enhancement""] }.collectEntries 
            { [ (it.optionId.toString()) : it.getValue() ] } as Map
                newOrEnhancement.setOptionsVisibility(cfA);
                console.log(""Custom Option List: "" + cfA);
            clientLevel.setRequired(true);
            reportName.setVisible(false);
                reportName.setRequired(false);

                //Start (JIRA-5208)
                purpose.setVisible(false);
                purpose.setRequired(false);
                clientType.setVisible(false);
                clientType.setRequired(false);
                services.setVisible(false);
                services.setRequired(false);
                products.setVisible(false);
                clientName.setVisible(false);
                clientName.setRequired(false);
                groupName.setVisible(false);
                recipientCompany.setVisible(false);
                recipientCompany.setRequired(false);
                recipientType.setVisible(false);
                recipientType.setRequired(false);
                dataDeliveryApprovalReason.setVisible(false);
                privacyApproval.setVisible(false);
                privacyApprovalComments.setVisible(false); 
                informationSecurityApproval.setVisible(false); 
                informationSecurityApprovalComments.setVisible(false); 
                clinicalAnalyticsApproval.setVisible(false);
                clinicalAnalyticsApprovalComments.setVisible(false);
                pricing.setVisible(false);
                levelOfEffort.setVisible(false);
                dataScienceApproval.setVisible(false);
                dataScienceApprovalComments.setVisible(false);
                clientLaunchDate.setVisible(false);
                // End (JIRA-5208)
                
                // Start ER 2/2024 JIRA-8908
                noOfEligibleLivesForTheClient.setVisible(true);
                noOfEligibleLivesForTheClient.setRequired(true);
                isThisReusable.setVisible(true);
            isThisReusable.setRequired(true);
                strategicValueOfRequest.setVisible(true);
                strategicValueOfRequest.setRequired(true);
                isThereAWorkaroundToGetYourDataOrSolution.setVisible(true);
                isThereAWorkaroundToGetYourDataOrSolution.setRequired(true);
                audience.setVisible(true);
                audience.setRequired(true);
            audienceDetails.setVisible(true);
                audienceDetails.setRequired(true);
                contractualObligation.setVisible(true);
                contractualObligation.setRequired(true);
                escalation.setVisible(true);
                escalation.setRequired(true);
                priorityScore.setVisible(true);
                // End ER 2/2024 JIRA-8908

            break;
    case ""Ad-hoc Analysis"":
            // Showing optional/required fields
            newOrEnhancement.setVisible(false);
            newOrEnhancement.setRequired(false);
            clientLevel.setRequired(true);
            isThisReusable.setVisible(true);// ER 2/2024 JIRA-8908
            isThisReusable.setRequired(true);// ER 2/2024 JIRA-8908
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sOWorBRDRequired.setRequired(true);
            sensitiveInformation.setVisible(true);
            sensitiveInformation.setRequired(true);
            dataSharingAgreement.setVisible(true);
            // dataSharingAgreement.setRequired(true);
            fileTypePGPEncryption.setVisible(false);
            fileTypeExtension.setVisible(false);
            fileTypeDelimiter.setVisible(false);
            extensionDetails.setVisible(false);
            testFileRequired.setVisible(false);
            testFileRequired.setRequired(false);
            frequency.setVisible(false);
            frequency.setRequired(false);
            modeOfDelivery.setVisible(false);
            modeOfDelivery.setRequired(false);
            modeOfDeliveryVendor.setVisible(true);
            modeOfDeliveryVendor.setRequired(false);
            modeOfDeliveryEmail.setVisible(true);
            modeOfDeliveryEmail.setRequired(false);
            reportName.setVisible(false);
                reportName.setRequired(false);

                //Start (JIRA-5208)
                purpose.setVisible(false);
                purpose.setRequired(false);
                clientType.setVisible(false);
                clientType.setRequired(false);
                services.setVisible(false);
                services.setRequired(false);
                products.setVisible(false);
                clientName.setVisible(false);
                clientName.setRequired(false);
                groupName.setVisible(false);
                recipientCompany.setVisible(false);
                recipientCompany.setRequired(false);
                recipientType.setVisible(false);
                recipientType.setRequired(false);
                dataDeliveryApprovalReason.setVisible(false);
                privacyApproval.setVisible(false);
                privacyApprovalComments.setVisible(false); 
                informationSecurityApproval.setVisible(false); 
                informationSecurityApprovalComments.setVisible(false); 
                clinicalAnalyticsApproval.setVisible(false);
                clinicalAnalyticsApprovalComments.setVisible(false);
                pricing.setVisible(false);
                levelOfEffort.setVisible(false);
                dataScienceApproval.setVisible(false);
                dataScienceApprovalComments.setVisible(false);
                clientLaunchDate.setVisible(false);
                // End (JIRA-5208)

                // Start ER 2/2024 JIRA-8908
                noOfEligibleLivesForTheClient.setVisible(true);
                noOfEligibleLivesForTheClient.setRequired(true);
                isThisReusable.setVisible(true);
            isThisReusable.setRequired(true);
                strategicValueOfRequest.setVisible(true);
                strategicValueOfRequest.setRequired(true);
                isThereAWorkaroundToGetYourDataOrSolution.setVisible(true);
                isThereAWorkaroundToGetYourDataOrSolution.setRequired(true);
                audience.setVisible(true);
                audience.setRequired(true);
            audienceDetails.setVisible(true);
                audienceDetails.setRequired(true);
                contractualObligation.setVisible(true);
                contractualObligation.setRequired(true);
                escalation.setVisible(true);
                escalation.setRequired(true);
                priorityScore.setVisible(true);
                // End ER 2/2024 JIRA-8908
            break;
    case ""Tableau Dashboard Feedback"":
            // Showing optional/required fields
            newOrEnhancement.setVisible(false);
            newOrEnhancement.setRequired(false);
            clientLevel.setRequired(false);
            isThisReusable.setVisible(true);
            isThisReusable.setRequired(true);
            reportingTeamOwner.setVisible(false);
            approver.setVisible(false);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(false);
            sOWorBRDRequired.setVisible(false);
            sOWorBRDRequired.setRequired(false);
            sensitiveInformation.setVisible(false);
            sensitiveInformation.setRequired(false);
            dataSharingAgreement.setVisible(false);
            // dataSharingAgreement.setRequired(false);
            fileTypePGPEncryption.setVisible(false);
            fileTypeExtension.setVisible(false);
            fileTypeDelimiter.setVisible(false);
            extensionDetails.setVisible(false);
            testFileRequired.setVisible(false);
            testFileRequired.setRequired(false);
            frequency.setVisible(false);
            frequency.setRequired(false);
            modeOfDelivery.setVisible(false);
            modeOfDelivery.setRequired(false);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryVendor.setRequired(false);
            modeOfDeliveryEmail.setVisible(false);
            modeOfDeliveryEmail.setRequired(false);
            reportName.setVisible(false);
                reportName.setRequired(false);

                //Start (JIRA-5208)
                purpose.setVisible(false);
                purpose.setRequired(false);
                clientType.setVisible(false);
                clientType.setRequired(false);
                services.setVisible(false);
                services.setRequired(false);
                products.setVisible(false);
                clientName.setVisible(false);
                clientName.setRequired(false);
                groupName.setVisible(false);
                recipientCompany.setVisible(false);
                recipientCompany.setRequired(false);
                recipientType.setVisible(false);
                recipientType.setRequired(false);
                dataDeliveryApprovalReason.setVisible(false);
                privacyApproval.setVisible(false);
                privacyApprovalComments.setVisible(false); 
                informationSecurityApproval.setVisible(false); 
                informationSecurityApprovalComments.setVisible(false); 
                clinicalAnalyticsApproval.setVisible(false);
                clinicalAnalyticsApprovalComments.setVisible(false);
                pricing.setVisible(false);
                levelOfEffort.setVisible(false);
                dataScienceApproval.setVisible(false);
                dataScienceApprovalComments.setVisible(false);
                clientLaunchDate.setVisible(false);
                // End (JIRA-5208)

                // Start ER 2/2024 JIRA-8908
                noOfEligibleLivesForTheClient.setVisible(true);
                noOfEligibleLivesForTheClient.setRequired(true);
                isThisReusable.setVisible(true);
            isThisReusable.setRequired(true);
                strategicValueOfRequest.setVisible(true);
                strategicValueOfRequest.setRequired(true);
                isThereAWorkaroundToGetYourDataOrSolution.setVisible(true);
                isThereAWorkaroundToGetYourDataOrSolution.setRequired(true);
                audience.setVisible(true);
                audience.setRequired(true);
            audienceDetails.setVisible(true);
                audienceDetails.setRequired(true);
                contractualObligation.setVisible(true);
                contractualObligation.setRequired(true);
                escalation.setVisible(true);
                escalation.setRequired(true);
                priorityScore.setVisible(true);
                // End ER 2/2024 JIRA-8908
            break;
    default:
        break;
}
console.log(""Behaviours CRUS Project – Request Type completed."");","// Warning: Functionality lost -> getOptionsVisibility() only 
// works after options were already set, so it's unstable. 
// You cannot create a reliable generator, once options are updated in 
// the field there's risk of loss of functionality or wrong behaviors

// This listens if the ""Request Type"" field has changed
if (getChangeField(""customfield_10090"").getName() == ""Request Type"") {
    
    let requestType = getChangeField().getValue(); // Field (Request Type)

    // This saves custom field's ids to manage its behaviors
    let newOrEnhancement = getFieldById(""customfield_10090""); // Field (New or Enhancement)
    let standardDataExtractType = getFieldById(""customfield_10090""); // Field (Standard Data Extract Type)
    let reportName = getFieldById(""customfield_10090""); // Field (Report Name)
    let description = getFieldById(""customfield_10090""); // Field (Description)
    let clientLevel = getFieldById(""customfield_10090""); // Field (Client Level)
    let businessRequirements = getFieldById(""customfield_10090""); // Field (Business Requirements)
    let areThereAnyTags = getFieldById(""customfield_10090""); // Field (Are there any tags?)
    let clientName = getFieldById(""customfield_10090""); // Field (Client Name)
    let clientLaunchDate = getFieldById(""customfield_10090""); // Field (Client Launch Date)
    let contractLanguageForGrievanceReporting = getFieldById(""customfield_10090""); // Field (Contract Language for Grievance Reporting)
    let isThisReusable = getFieldById(""customfield_10090""); // Field (Is this Reusable?)
    let reportingTeamOwner = getFieldById(""customfield_10090""); // Field (Reporting Team Owner)
    let approver = getFieldById(""customfield_10090""); // Field (Approver)
    let audience = getFieldById(""customfield_10090""); // Field (Audience (SL))
    let audienceDetails = getFieldById(""customfield_10090""); // Field (Audience Details)
    let contractualObligation = getFieldById(""customfield_10090""); // Field (Contractual Obligation)
    let sOWorBRDRequired = getFieldById(""customfield_10090""); // Field (SOW or BRD required?)
    let sensitiveInformation = getFieldById(""customfield_10090""); // Field (Sensitive Information)
    let dataSharingAgreement = getFieldById(""customfield_10090""); // Field (Data Sharing Agreement)
    let fileTypePGPEncryption = getFieldById(""customfield_10090""); // Field (File Type PGP Encryption)
    let fileTypeExtension = getFieldById(""customfield_10090""); // Field (File Type Extension)
    let fileTypeDelimiter = getFieldById(""customfield_10090""); // Field (File Type Delimiter)
    let extensionDetails = getFieldById(""customfield_10090""); // Field (Extension Details)
    let testFileRequired = getFieldById(""customfield_10090""); // Field (Test file Required?)
    let frequency = getFieldById(""customfield_10090""); // Field (Frequency)
    let modeOfDelivery = getFieldById(""customfield_10090""); // Field (Mode of Delivery)
    let modeOfDeliveryVendor = getFieldById(""customfield_10090""); // Field (SFTP Host and Folder)
    let modeOfDeliveryEmail = getFieldById(""customfield_10090""); // Field (Email Delivery - Provide Email)
    let priorityScore = getFieldById(""customfield_10090""); // Field (Priority Score)
    let purpose = getFieldById(""customfield_10090""); // Field (Purpose)
    let clientType = getFieldById(""customfield_10090""); // Field (Client Type)
    let services = getFieldById(""customfield_10090""); // Field (Services)
    let products = getFieldById(""customfield_10090""); // Field (Products (TF))
    let groupName = getFieldById(""customfield_10090""); // Field (Group Name)
    let recipientCompany = getFieldById(""customfield_10090""); // Field (Recipient Company)
    let recipientType = getFieldById(""customfield_10090""); // Field (Recipient Type)
    let boxLocarion = getFieldById(""customfield_10090""); // Field (Box Location)
    let dataDeliveryApprovalReason = getFieldById(""customfield_10090""); // Field (Data Delivery Approval Reason)
    let privacyApproval = getFieldById(""customfield_10090""); // Field (Privacy Approval)
    let privacyApprovalComments = getFieldById(""customfield_10090""); // Field (Privacy Approval Comments)
    let informationSecurityApproval = getFieldById(""customfield_10090""); // Field (Information Security Approval)
    let informationSecurityApprovalComments = getFieldById(""customfield_10090""); // Field (Information Security Approval Comments)
    let clinicalAnalyticsApproval = getFieldById(""customfield_10090""); // Field (Clinical Analytics Approval)
    let clinicalAnalyticsApprovalComments = getFieldById(""customfield_10090""); // Field (Clinical Analytics Approval Comments)
    let dataScienceApproval = getFieldById(""customfield_10090""); // Field (Data Science Approval)
    let dataScienceApprovalComments = getFieldById(""customfield_10090""); // Field (Data Science Approval Comments)
    let pricing = getFieldById(""customfield_10090""); // Field (Pricing (MC))
    let levelOfEffort = getFieldById(""customfield_10090""); // Field (Level of Effort)
    let noOfEligibleLivesForTheClient = getFieldById(""customfield_10090""); // Field (No. of Eligible Lives for the Client)
    let strategicValueOfRequest = getFieldById(""customfield_10090""); // Field (Strategic Value of Request)
    let isThereAWorkaroundToGetYourDataOrSolution = getFieldById(""customfield_10090""); // Field (Is there a workaround to get your data or solution?)
    let escalation = getFieldById(""customfield_10090""); // Field (Escalation)

    // This gets the value of ""Request Type"" as String
    let requestTypeOption = requestType.getValue().value

    switch(requestTypeOption){
        case ""Data Extract | Member/Visit/Event level"":
            // Showing/Requiring Report Type
            newOrEnhancement.setVisible(true);
            newOrEnhancement.setRequired(true);
            // let cfA = cfOptions.findAll 
            // { it.getValue() in [""----"",""Standard - New"",""Standard - Enhancement"",""Custom - New"",""Custom - Enhancement""] }.collectEntries 
            // { [ (it.optionId.toString()) : it.getValue() ] } as Map
            // newOrEnhancement.setOptionsVisibility(cfA);
            clientLevel.setRequired(false);
            reportName.setVisible(false);
            reportName.setRequired(false);
            dataSharingAgreement.setVisible(true);
            purpose.setVisible(true);
            purpose.setRequired(true);
            clientType.setVisible(true);
            clientType.setRequired(true);
            services.setVisible(true);
            services.setRequired(true);
            products.setVisible(true);
            clientName.setVisible(true);
            clientName.setRequired(true);
            groupName.setVisible(true);
            recipientCompany.setVisible(true);
            recipientCompany.setRequired(true);
            recipientType.setVisible(true);
            recipientType.setRequired(true);
            dataDeliveryApprovalReason.setVisible(true);
            privacyApproval.setVisible(true);
            privacyApprovalComments.setVisible(true); 
            informationSecurityApproval.setVisible(true); 
            informationSecurityApprovalComments.setVisible(true); 
            clinicalAnalyticsApproval.setVisible(true);
            clinicalAnalyticsApprovalComments.setVisible(true);
            pricing.setVisible(true);
            levelOfEffort.setVisible(true);
            dataScienceApproval.setVisible(true);
            dataScienceApprovalComments.setVisible(true);
            clientLaunchDate.setVisible(true);
            modeOfDelivery.setVisible(true);
            modeOfDelivery.setRequired(true);
            noOfEligibleLivesForTheClient.setVisible(false);
            noOfEligibleLivesForTheClient.setRequired(false);
            isThisReusable.setVisible(true);
            strategicValueOfRequest.setVisible(false);
            strategicValueOfRequest.setRequired(false);
            isThereAWorkaroundToGetYourDataOrSolution.setVisible(false);
            isThereAWorkaroundToGetYourDataOrSolution.setRequired(false);
            audience.setVisible(true);
            audienceDetails.setVisible(true);
            contractualObligation.setVisible(true);
            escalation.setVisible(false);
            escalation.setRequired(false);
            priorityScore.setVisible(false);
            break;

        case ""Client Report Feedback"":
            // Showing optional/required fields
            newOrEnhancement.setVisible(false);
            newOrEnhancement.setRequired(false);
            clientLevel.setRequired(false);
            isThisReusable.setVisible(true);
            isThisReusable.setRequired(true);
            reportingTeamOwner.setVisible(false);
            approver.setVisible(false);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(false);
            sOWorBRDRequired.setVisible(false);
            sOWorBRDRequired.setRequired(false);
            sensitiveInformation.setVisible(false);
            sensitiveInformation.setRequired(false);
            dataSharingAgreement.setVisible(false);
            fileTypePGPEncryption.setVisible(false);
            fileTypeExtension.setVisible(false);
            fileTypeDelimiter.setVisible(false);
            extensionDetails.setVisible(false);
            testFileRequired.setVisible(false);
            testFileRequired.setRequired(false);
            frequency.setVisible(false);
            frequency.setRequired(false);
            modeOfDelivery.setVisible(false);
            modeOfDelivery.setRequired(false);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryVendor.setRequired(false);
            modeOfDeliveryEmail.setVisible(false);
            modeOfDeliveryEmail.setRequired(false);
            reportName.setVisible(true);
            reportName.setRequired(false);
            purpose.setVisible(false);
            purpose.setRequired(false);
            clientType.setVisible(false);
            clientType.setRequired(false);
            services.setVisible(false);
            services.setRequired(false);
            products.setVisible(false);
            clientName.setVisible(false);
            clientName.setRequired(false);
            groupName.setVisible(false);
            recipientCompany.setVisible(false);
            recipientCompany.setRequired(false);
            recipientType.setVisible(false);
            recipientType.setRequired(false);
            dataDeliveryApprovalReason.setVisible(false);
            privacyApproval.setVisible(false);
            privacyApprovalComments.setVisible(false); 
            informationSecurityApproval.setVisible(false); 
            informationSecurityApprovalComments.setVisible(false); 
            clinicalAnalyticsApproval.setVisible(false);
            clinicalAnalyticsApprovalComments.setVisible(false);
            pricing.setVisible(false);
            levelOfEffort.setVisible(false);
            dataScienceApproval.setVisible(false);
            dataScienceApprovalComments.setVisible(false);
            clientLaunchDate.setVisible(false);
            noOfEligibleLivesForTheClient.setVisible(true);
            noOfEligibleLivesForTheClient.setRequired(true);
            isThisReusable.setVisible(true);
            isThisReusable.setRequired(true);
            strategicValueOfRequest.setVisible(true);
            strategicValueOfRequest.setRequired(true);
            isThereAWorkaroundToGetYourDataOrSolution.setVisible(true);
            isThereAWorkaroundToGetYourDataOrSolution.setRequired(true);
            audience.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setVisible(true);
            audienceDetails.setRequired(true);
            contractualObligation.setVisible(true);
            contractualObligation.setRequired(true);
            escalation.setVisible(true);
            escalation.setRequired(true);
            priorityScore.setVisible(true);
            break;

        case ""Custom Client Report | Aggregated/Summary"":
            // Showing/Requiring Report Type
            newOrEnhancement.setVisible(true);
            newOrEnhancement.setRequired(true);
            // Showing only some values of the original list
            // let cfA = cfOptions.findAll 
            // { it.getValue() in [""----"",""New"",""Enhancement""] }.collectEntries 
            //{ [ (it.optionId.toString()) : it.getValue() ] } as Map
            // newOrEnhancement.setOptionsVisibility(cfA);
            clientLevel.setRequired(true);
            reportName.setVisible(false);
            reportName.setRequired(false);
            purpose.setVisible(false);
            purpose.setRequired(false);
            clientType.setVisible(false);
            clientType.setRequired(false);
            services.setVisible(false);
            services.setRequired(false);
            products.setVisible(false);
            clientName.setVisible(false);
            clientName.setRequired(false);
            groupName.setVisible(false);
            recipientCompany.setVisible(false);
            recipientCompany.setRequired(false);
            recipientType.setVisible(false);
            recipientType.setRequired(false);
            dataDeliveryApprovalReason.setVisible(false);
            privacyApproval.setVisible(false);
            privacyApprovalComments.setVisible(false); 
            informationSecurityApproval.setVisible(false); 
            informationSecurityApprovalComments.setVisible(false); 
            clinicalAnalyticsApproval.setVisible(false);
            clinicalAnalyticsApprovalComments.setVisible(false);
            pricing.setVisible(false);
            levelOfEffort.setVisible(false);
            dataScienceApproval.setVisible(false);
            dataScienceApprovalComments.setVisible(false);
            clientLaunchDate.setVisible(false);
            noOfEligibleLivesForTheClient.setVisible(true);
            noOfEligibleLivesForTheClient.setRequired(true);
            isThisReusable.setVisible(true);
            isThisReusable.setRequired(true);
            strategicValueOfRequest.setVisible(true);
            strategicValueOfRequest.setRequired(true);
            isThereAWorkaroundToGetYourDataOrSolution.setVisible(true);
            isThereAWorkaroundToGetYourDataOrSolution.setRequired(true);
            audience.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setVisible(true);
            audienceDetails.setRequired(true);
            contractualObligation.setVisible(true);
            contractualObligation.setRequired(true);
            escalation.setVisible(true);
            escalation.setRequired(true);
            priorityScore.setVisible(true);
            // End ER 2/2024 JIRA-8908

            break;

        case ""Ad-hoc Analysis"":
            // Showing optional/required fields
            newOrEnhancement.setVisible(false);
            newOrEnhancement.setRequired(false);
            clientLevel.setRequired(true);
            isThisReusable.setVisible(true);// ER 2/2024 JIRA-8908
            isThisReusable.setRequired(true);// ER 2/2024 JIRA-8908
            reportingTeamOwner.setVisible(true);
            approver.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setRequired(true);
            contractualObligation.setRequired(true);
            sOWorBRDRequired.setVisible(true);
            sOWorBRDRequired.setRequired(true);
            sensitiveInformation.setVisible(true);
            sensitiveInformation.setRequired(true);
            dataSharingAgreement.setVisible(true);
            fileTypePGPEncryption.setVisible(false);
            fileTypeExtension.setVisible(false);
            fileTypeDelimiter.setVisible(false);
            extensionDetails.setVisible(false);
            testFileRequired.setVisible(false);
            testFileRequired.setRequired(false);
            frequency.setVisible(false);
            frequency.setRequired(false);
            modeOfDelivery.setVisible(false);
            modeOfDelivery.setRequired(false);
            modeOfDeliveryVendor.setVisible(true);
            modeOfDeliveryVendor.setRequired(false);
            modeOfDeliveryEmail.setVisible(true);
            modeOfDeliveryEmail.setRequired(false);
            reportName.setVisible(false);
            reportName.setRequired(false);   
            purpose.setVisible(false);
            purpose.setRequired(false);
            clientType.setVisible(false);
            clientType.setRequired(false);
            services.setVisible(false);
            services.setRequired(false);
            products.setVisible(false);
            clientName.setVisible(false);
            clientName.setRequired(false);
            groupName.setVisible(false);
            recipientCompany.setVisible(false);
            recipientCompany.setRequired(false);
            recipientType.setVisible(false);
            recipientType.setRequired(false);
            dataDeliveryApprovalReason.setVisible(false);
            privacyApproval.setVisible(false);
            privacyApprovalComments.setVisible(false); 
            informationSecurityApproval.setVisible(false); 
            informationSecurityApprovalComments.setVisible(false); 
            clinicalAnalyticsApproval.setVisible(false);
            clinicalAnalyticsApprovalComments.setVisible(false);
            pricing.setVisible(false);
            levelOfEffort.setVisible(false);
            dataScienceApproval.setVisible(false);
            dataScienceApprovalComments.setVisible(false);
            clientLaunchDate.setVisible(false);
            noOfEligibleLivesForTheClient.setVisible(true);
            noOfEligibleLivesForTheClient.setRequired(true);
            isThisReusable.setVisible(true);
            isThisReusable.setRequired(true);
            strategicValueOfRequest.setVisible(true);
            strategicValueOfRequest.setRequired(true);
            isThereAWorkaroundToGetYourDataOrSolution.setVisible(true);
            isThereAWorkaroundToGetYourDataOrSolution.setRequired(true);
            audience.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setVisible(true);
            audienceDetails.setRequired(true);
            contractualObligation.setVisible(true);
            contractualObligation.setRequired(true);
            escalation.setVisible(true);
            escalation.setRequired(true);
            priorityScore.setVisible(true);
            
            break;
        
        case ""Tableau Dashboard Feedback"":
            // Showing optional/required fields
            newOrEnhancement.setVisible(false);
            newOrEnhancement.setRequired(false);
            clientLevel.setRequired(false);
            isThisReusable.setVisible(true);
            isThisReusable.setRequired(true);
            reportingTeamOwner.setVisible(false);
            approver.setVisible(false);
            audience.setRequired(false);
            audienceDetails.setRequired(false);
            contractualObligation.setRequired(false);
            sOWorBRDRequired.setVisible(false);
            sOWorBRDRequired.setRequired(false);
            sensitiveInformation.setVisible(false);
            sensitiveInformation.setRequired(false);
            dataSharingAgreement.setVisible(false);
            fileTypePGPEncryption.setVisible(false);
            fileTypeExtension.setVisible(false);
            fileTypeDelimiter.setVisible(false);
            extensionDetails.setVisible(false);
            testFileRequired.setVisible(false);
            testFileRequired.setRequired(false);
            frequency.setVisible(false);
            frequency.setRequired(false);
            modeOfDelivery.setVisible(false);
            modeOfDelivery.setRequired(false);
            modeOfDeliveryVendor.setVisible(false);
            modeOfDeliveryVendor.setRequired(false);
            modeOfDeliveryEmail.setVisible(false);
            modeOfDeliveryEmail.setRequired(false);
            reportName.setVisible(false);
            reportName.setRequired(false);
            purpose.setVisible(false);
            purpose.setRequired(false);
            clientType.setVisible(false);
            clientType.setRequired(false);
            services.setVisible(false);
            services.setRequired(false);
            products.setVisible(false);
            clientName.setVisible(false);
            clientName.setRequired(false);
            groupName.setVisible(false);
            recipientCompany.setVisible(false);
            recipientCompany.setRequired(false);
            recipientType.setVisible(false);
            recipientType.setRequired(false);
            dataDeliveryApprovalReason.setVisible(false);
            privacyApproval.setVisible(false);
            privacyApprovalComments.setVisible(false); 
            informationSecurityApproval.setVisible(false); 
            informationSecurityApprovalComments.setVisible(false); 
            clinicalAnalyticsApproval.setVisible(false);
            clinicalAnalyticsApprovalComments.setVisible(false);
            pricing.setVisible(false);
            levelOfEffort.setVisible(false);
            dataScienceApproval.setVisible(false);
            dataScienceApprovalComments.setVisible(false);
            clientLaunchDate.setVisible(false);
            noOfEligibleLivesForTheClient.setVisible(true);
            noOfEligibleLivesForTheClient.setRequired(true);
            isThisReusable.setVisible(true);
            isThisReusable.setRequired(true);
            strategicValueOfRequest.setVisible(true);
            strategicValueOfRequest.setRequired(true);
            isThereAWorkaroundToGetYourDataOrSolution.setVisible(true);
            isThereAWorkaroundToGetYourDataOrSolution.setRequired(true);
            audience.setVisible(true);
            audience.setRequired(true);
            audienceDetails.setVisible(true);
            audienceDetails.setRequired(true);
            contractualObligation.setVisible(true);
            contractualObligation.setRequired(true);
            escalation.setVisible(true);
            escalation.setRequired(true);
            priorityScore.setVisible(true);
                
            break;
        default:
            break;
    }
}

"
11,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**import org.apache.jasper.Options

* Eduardo Rojo
* CRUS project - Client Reporting - USGH
* Issue Type: Story
* Hide fields based on ""Client Level""
* Log: 4/3/2023 JIRA-5208
*/

console.log(""Behaviours CRUS Project – Client Level started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Client Level"" field has changed
let clientLevel = getFieldById(getFieldChanged());

// This saves the custom field ""SFTP - Client/Vendor Details"" and ""Email Delivery - Provide Email""
let groupId = getFieldById(""Group ID, Org ID or Client Code"");

// This gets the value of ""Mode of Delivery"" field
let clientLevelOption = clientLevel.getValue() as String

console.log(""Client Level: "" + clientLevelOption);

// Set  ""Group ID, Org ID or Client Code"" field as Required
if(clientLevelOption){
	groupId.setRequired(true);
	}
else{
	groupId.setRequired(false);
	}
console.log(""Behaviours CRUS Project – Client Level finished."");","
if (getChangeField().getName() == ""Client Level"") {
    let clientLevel = getChangeField().getValue();    

    // This saves the custom field ""SFTP - Client/Vendor Details"" and ""Email Delivery - Provide Email""
    let groupId = getFieldById(""customfield_10090""); // Field (Group ID, Org ID or Client Code)

    // This gets the value of ""Mode of Delivery"" field
    let clientLevelOption = clientLevel.value

    // Set  ""Group ID, Org ID or Client Code"" field as Required
    if(clientLevelOption){
         groupId.setRequired(true);
    }
    else {
        groupId.setRequired(false);
    }
}
"
12,"// Had Imports
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// static com.atlassian.jira.issue.IssueFieldConstants.*
/*Creator: Jeff Melies
Purpose: For ""Bug"",""Task"",""Story"" Issue Types If /
         Required RS/VE Update is Yes, make Requirements Documentation /
         a required field.
*/

//Imports
    
@BaseScript FieldBehaviours fieldBehaviours

//Logging
console.log(""Behaviours COMMS Project  'Requires RS/VE Update' has started"")

// This listens if the ""Requires RS/VE Update"" field has changed
let reqRSVEUpdate = getFieldById(getFieldChanged())
console.log(""Requires RS/VE Update field: "" + reqRSVEUpdate.getValue())
let issueTypeField = getFieldById(ISSUE_TYPE)  //Get Issue Type Field object
let reqDocumentation = getFieldById(""Requirements Documentation"")

//*******Only execute if the Issue Type is Bug, Task, and Story see Jira-721
console.log(""Issue Type Field value: "" + issueTypeField.getValue())
if (!([""Bug"",""Task"",""Story""].includes(issueTypeField.getValue()))) {}
//reqRSVEUpdate.setRequired(true)
if (reqRSVEUpdate.getValue() == ""Yes""){
    reqDocumentation.setVisible(true)
    reqDocumentation.setRequired(true)
}else{
    reqDocumentation.setVisible(false)
    reqDocumentation.setRequired(false)
}

console.log(""Behaviours COMMS Project  'Requires RS/VE Update' has completed."")","const context = await getContext()
const issueType = context.extension.issueType.name

// This listens if the ""Requires RS/VE Update"" field has changed
if (getChangeField().getName() == ""Requires RS/VE Update"") {
     
    let reqDocumentation = getFieldById(""customfield_10090"") // Field (Requirements Documentation)

    // Only execute if the Issue Type is Bug, Task, and Story see Jira-721
    if ([""Bug"",""Task"",""Story""].includes(issueType)) {

        if (getChangeField().getValue() == ""Yes""){
            reqDocumentation.setVisible(true)
            reqDocumentation.setRequired(true)
        } else {
            reqDocumentation.setVisible(false)
            reqDocumentation.setRequired(false)
        }

    }    
}
"
13,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Logger
//Creator: Jeff Melies
//Purpose: Show other fields dependent on this one
//Change log:

//imports

@BaseScript FieldBehaviours fieldBehaviours

log = Logger.getLogger(""Behaviour: UI Design"")

let projectName = issueContext?.projectObject?.name

//Logging
log.debug(""${projectName}-'Design/Content Status' has started"")

let changedFieldValue = getFieldById(getFieldChanged())?.getValue()

if( changedFieldValue != null && changedFieldValue.toString() != ""No Design Needed""){
        let notes = getFieldById(""Design/Content Notes"")?.setVisible(true)
        let designer = getFieldById(""Designer"")?.setVisible(true)
        let approvalCO = getFieldById(""Design/Content Approvals"")?.setVisible(true)
        let strategist = getFieldById(""Content Strategist"")?.setVisible(true)
}else{
        let notes = getFieldById(""Design/Content Notes"")?.setVisible(false)
        let designer = getFieldById(""Designer"")?.setVisible(false)
        let approvalCO = getFieldById(""Design/Content Approvals"")?.setVisible(false)
        let strategist = getFieldById(""Content Strategist"")?.setVisible(false)
}

log.debug(""${projectName}-'Design/Content Status' has completed"")","! need more information on which field is being edited, I thought it would be Design/Content Status[Dropdown] but the option it's checking does not exist, so probably not the correct field."
14,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
// com.onresolve.jira.groovy.user.FieldBehaviours;
// groovy.transform.BaseScript;
// Jeff Tompkins
// HEAL & COP projects - Bug, Story, Task
// Hide Design Fields if Design Status is empty


//Managers
let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Design Status"" field has changed
let designStatus = getFieldById(getFieldChanged()); 

// This fetches the names of the other 3 Design custom fields
let approvalCheckoff = getFieldById(""Approval Checkoff"");
let designer = getFieldById(""Designer"");
let notes = getFieldById(""Notes"");

// This gets the value of the Design Status field
let designStatusOption = designStatus.getValue() as String
// console.log(""Design Status selected: "" + designStatusOption)

// Hide the 3 Design fields
if (designStatusOption == null) {
    // hide Approval Checkoff field
    approvalCheckoff.setVisible(false);
    // hide Designer field
    designer.setVisible(false);
    // hide Notes field
    notes.setVisible(false);
// else Show the 3 Design fields
} else {
    // show Approval Checkoff field
    approvalCheckoff.setVisible(true);
    // show Designer field
    designer.setVisible(true);
    // show Notes field
    notes.setVisible(true);
}","
// This listens if the ""Design Status"" field has changed

if (getChangeField().getName() == ""Design/Content Status"") {
    let designStatus = getChangeField().getValue().value;


    // This fetches the names of the other 3 Design custom fields
    let approvalCheckoff = getFieldById(""customfield_10090""); // Field (Approval Checkoff);
    let designer = getFieldById(""customfield_10090""); // Field (Designer);
    let notes = getFieldById(""customfield_10090""); // Field (Notes);


    // Hide the 3 Design fields
    if (designStatusOption == null) {
        // hide Approval Checkoff field
        approvalCheckoff.setVisible(false);

        // hide Designer field
        designer.setVisible(false);

        // hide Notes field
        notes.setVisible(false);

    // else Show the 3 Design fields
    } else {
        // show Approval Checkoff field
        approvalCheckoff.setVisible(true);

        // show Designer field
        designer.setVisible(true);
        
        // show Notes field
        notes.setVisible(true);
    }


}
"
15,"

",--
16,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
// com.atlassian.jira.issue.customfields.option.Options
/**import org.apache.jasper.Options

 * Eduardo Rojo
 * IDT project - IM Data Transfer (IDT)
 * Issue Type: All
 * Set required/optional fields based on ""Request Type"" value
 * Log: 9/26/2023 Eduardo Rojo (JIRA-8410) 
 */
console.log(""Behaviours IDT Project – Request Type started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Request Type"" field has changed
let requestType = getFieldById(getFieldChanged());

// This saves custom field's ids to manage its behaviors
let summary = getFieldById(""Summary"");
let projectName = getFieldById(""Project Name"");
let description = getFieldById(""Description"");
let clientName = getFieldById(""Client Name"");
let groupId = getFieldById(""Group ID"");
let eligibilityVerificationMethod = getFieldById(""Eligibility Verification Method"");
let partnerName = getFieldById(""Partner Name"");
let mappingDetails = getFieldById(""Mapping Details"");
let launchDate = getFieldById(""Launch Date"");
let sftpDetails = getFieldById(""SFTP Details"");
let attachment = getFieldById(""Attachment"");


summary.setDescription(""A brief overview to give an idea of what the request is about."");
projectName.setDescription(""The name of the project this request is/will be part of."");
description.setDescription(""The objective of this request. Feel free to also mention anything else you would like to share."");
clientName.setDescription(""Client Name(s) from Admin."");
groupId.setDescription(""Legacy Group Id(s) / Admin Group (No org IDs)."");
eligibilityVerificationMethod.setDescription(""The eligibility type (primary)."");
partnerName.setDescription(""Please specify old vendor and new vendor name, if applicable."");
launchDate.setDescription(""The launch date of the client."");
sftpDetails.setDescription(""SFTP details should include contact and atleast one IP Address. If existing, provide username. Please also attach the Eligibility Questionnaire."");
mappingDetails.setDescription(""Please attach Mapping document."");
attachment.setDescription(""Do not attach any kind of PII/PHI, instead please share a box link."");

// This gets the value of ""Request Type"" as String
let requestTypeOption = requestType.getValue() as String

switch(requestTypeOption){
    case ""Configure or Investigate SFTP"":
		projectName.setRequired(true);
		description.setRequired(true);
		clientName.setRequired(true);
		groupId.setRequired(true);
		eligibilityVerificationMethod.setRequired(true);
		partnerName.setRequired(true);
		launchDate.setRequired(true);
		sftpDetails.setRequired(true);
		mappingDetails.setRequired(false);
		attachment.setRequired(true);
    	break;
    case ""Review and/or Automate New Data"":
    	projectName.setRequired(true);
		description.setRequired(true);
		clientName.setRequired(true);
		groupId.setRequired(true);
		eligibilityVerificationMethod.setRequired(true);
		partnerName.setRequired(true);
		launchDate.setRequired(true);
		sftpDetails.setRequired(true);
		mappingDetails.setRequired(true);
		attachment.setRequired(false);
    	break;
    case ""Review and/or Analyze Existing Data"":
    	projectName.setRequired(true);
		description.setRequired(true);
		clientName.setRequired(true);
		groupId.setRequired(true);
		eligibilityVerificationMethod.setRequired(false);
		partnerName.setRequired(false);
		launchDate.setRequired(false);
		sftpDetails.setRequired(false);
		mappingDetails.setRequired(false);
		attachment.setRequired(false);
    	break;
    case ""Update Members"":
    	projectName.setRequired(true);
		description.setRequired(true);
		clientName.setRequired(true);
		groupId.setRequired(true);
		eligibilityVerificationMethod.setRequired(false);
		partnerName.setRequired(false);
		launchDate.setRequired(false);
		sftpDetails.setRequired(false);
		mappingDetails.setRequired(false);
		attachment.setRequired(false);
    	break;
    case ""Update Admin"":
    	projectName.setRequired(true);
		description.setRequired(true);
		clientName.setRequired(true);
		groupId.setRequired(true);
		eligibilityVerificationMethod.setRequired(false);
		partnerName.setRequired(false);
		launchDate.setRequired(true);
		sftpDetails.setRequired(false);
		mappingDetails.setRequired(false);
		attachment.setRequired(true);
    	break;
	case ""Other"":
    	projectName.setRequired(true);
		description.setRequired(true);
		clientName.setRequired(true);
		groupId.setRequired(true);
		eligibilityVerificationMethod.setRequired(false);
		partnerName.setRequired(false);
		launchDate.setRequired(false);
		sftpDetails.setRequired(false);
		mappingDetails.setRequired(false);
		attachment.setRequired(false);
    	break;
	case ""Other - Internal"":
    	projectName.setRequired(true);
		description.setRequired(true);
		clientName.setRequired(false);
		groupId.setRequired(false);
		eligibilityVerificationMethod.setRequired(false);
		partnerName.setRequired(false);
		launchDate.setRequired(false);
		sftpDetails.setRequired(false);
		mappingDetails.setRequired(false);
		attachment.setRequired(false);
    	break;
	case ""Ingestion Failure"":
    	projectName.setRequired(false);
		description.setRequired(false);
		clientName.setRequired(false);
		groupId.setRequired(false);
		eligibilityVerificationMethod.setRequired(false);
		partnerName.setRequired(false);
		launchDate.setRequired(false);
		sftpDetails.setRequired(false);
		mappingDetails.setRequired(false);
		attachment.setRequired(false);
    	break;
    default:
        break;
}
console.log(""Behaviours IDT Project – Request Type completed."");","// This listens if the ""Request Type"" field has changed
if (getChangeField().getName() == ""Request Type"") {
    let requestType = getChangeField()

    // This saves custom field's ids to manage its behaviors
    let summary = getFieldById(""customfield_10090"") // Field (Summary);
    let projectName = getFieldById(""customfield_10090"") // Field (Project Name);
    let description = getFieldById(""customfield_10090"") // Field (Description);
    let clientName = getFieldById(""customfield_10090"") // Field (Client Name);
    let groupId = getFieldById(""customfield_10090"") // Field (Group ID);
    let eligibilityVerificationMethod = getFieldById(""customfield_10090"") // Field (Eligibility Verification Method);
    let partnerName = getFieldById(""customfield_10090"") // Field (Partner Name);
    let mappingDetails = getFieldById(""customfield_10090"") // Field (Mapping Details);
    let launchDate = getFieldById(""customfield_10090"") // Field (Launch Date);
    let sftpDetails = getFieldById(""customfield_10090"") // Field (SFTP Details);
    let attachment = getFieldById(""customfield_10090"") // Field (Attachment);

    // sets descriptions for the fields mentioned above.
    summary.setDescription(""A brief overview to give an idea of what the request is about."");
    projectName.setDescription(""The name of the project this request is/will be part of."");
    description.setDescription(""The objective of this request. Feel free to also mention anything else you would like to share."");
    clientName.setDescription(""Client Name(s) from Admin."");
    groupId.setDescription(""Legacy Group Id(s) / Admin Group (No org IDs)."");
    eligibilityVerificationMethod.setDescription(""The eligibility type (primary)."");
    partnerName.setDescription(""Please specify old vendor and new vendor name, if applicable."");
    launchDate.setDescription(""The launch date of the client."");
    sftpDetails.setDescription(""SFTP details should include contact and atleast one IP Address. If existing, provide username. Please also attach the Eligibility Questionnaire."");
    mappingDetails.setDescription(""Please attach Mapping document."");
    attachment.setDescription(""Do not attach any kind of PII/PHI, instead please share a box link."");

    // This gets the value of ""Request Type"" as String
    let requestTypeOption = requestType.getValue().value 

    switch(requestTypeOption){
        case ""Configure or Investigate SFTP"":
             projectName.setRequired(true);
             description.setRequired(true);
             clientName.setRequired(true);
             groupId.setRequired(true);
             eligibilityVerificationMethod.setRequired(true);
             partnerName.setRequired(true);
             launchDate.setRequired(true);
             sftpDetails.setRequired(true);
             mappingDetails.setRequired(false);
             attachment.setRequired(true);
             break;
        case ""Review and/or Automate New Data"":
            projectName.setRequired(true);
             description.setRequired(true);
             clientName.setRequired(true);
             groupId.setRequired(true);
             eligibilityVerificationMethod.setRequired(true);
             partnerName.setRequired(true);
             launchDate.setRequired(true);
             sftpDetails.setRequired(true);
             mappingDetails.setRequired(true);
             attachment.setRequired(false);
             break;
        case ""Review and/or Analyze Existing Data"":
            projectName.setRequired(true);
             description.setRequired(true);
             clientName.setRequired(true);
             groupId.setRequired(true);
             eligibilityVerificationMethod.setRequired(false);
             partnerName.setRequired(false);
             launchDate.setRequired(false);
             sftpDetails.setRequired(false);
             mappingDetails.setRequired(false);
             attachment.setRequired(false);
             break;
        case ""Update Members"":
            projectName.setRequired(true);
             description.setRequired(true);
             clientName.setRequired(true);
             groupId.setRequired(true);
             eligibilityVerificationMethod.setRequired(false);
             partnerName.setRequired(false);
             launchDate.setRequired(false);
             sftpDetails.setRequired(false);
             mappingDetails.setRequired(false);
             attachment.setRequired(false);
             break;
        case ""Update Admin"":
            projectName.setRequired(true);
             description.setRequired(true);
             clientName.setRequired(true);
             groupId.setRequired(true);
             eligibilityVerificationMethod.setRequired(false);
             partnerName.setRequired(false);
             launchDate.setRequired(true);
             sftpDetails.setRequired(false);
             mappingDetails.setRequired(false);
             attachment.setRequired(true);
             break;
        case ""Other"":
            projectName.setRequired(true);
             description.setRequired(true);
             clientName.setRequired(true);
             groupId.setRequired(true);
             eligibilityVerificationMethod.setRequired(false);
             partnerName.setRequired(false);
             launchDate.setRequired(false);
             sftpDetails.setRequired(false);
             mappingDetails.setRequired(false);
             attachment.setRequired(false);
             break;
        case ""Other - Internal"":
            projectName.setRequired(true);
             description.setRequired(true);
             clientName.setRequired(false);
             groupId.setRequired(false);
             eligibilityVerificationMethod.setRequired(false);
             partnerName.setRequired(false);
             launchDate.setRequired(false);
             sftpDetails.setRequired(false);
             mappingDetails.setRequired(false);
             attachment.setRequired(false);
             break;
        case ""Ingestion Failure"":
            projectName.setRequired(false);
             description.setRequired(false);
             clientName.setRequired(false);
             groupId.setRequired(false);
             eligibilityVerificationMethod.setRequired(false);
             partnerName.setRequired(false);
             launchDate.setRequired(false);
             sftpDetails.setRequired(false);
             mappingDetails.setRequired(false);
             attachment.setRequired(false);
             break;
        default:
             break;
    }
}"
17,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Author: Eduardo Rojo
 * DATAOPS: CCM Data Operations - project
 * Issue Types: Story, Task
 * Set Require/Optional fields based on ""Request Type"" 
 * Log changes:
 */
console.log(""Behaviours DATAOPS Project – Request Type started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Request Type"" field has changed
let requestType = getFieldById(getFieldChanged());

// This saves custom field's ids to manage its behaviors
let dueDate = getFieldById(""Due Date"");
let clientName = getFieldById(""Client Name"");
let linkToSfRecord = getFieldById(""Link to SF Record"");
let clientCode = getFieldById(""Client Code"");
let partnerName = getFieldById(""Partner Name"");
let link = getFieldById(""Link"");
let namingConvention = getFieldById(""Naming Convention"");
let frequency = getFieldById(""Frequency"");
let multitenant = getFieldById(""Multitenant"");
let fileType = getFieldById(""File Type"");
let useCase = getFieldById(""Use Case"");
let mappingDetails = getFieldById(""Mapping Details"");
let sftpDetails = getFieldById(""SFTP Details"");

// This gets the value of ""Request Type"" as String
let requestTypeOption = requestType.getValue() as String

//dueDate.setRequired(true);
//clientName.setRequired(true);
//linkToSfRecord.setRequired(true);
//clientCode.setRequired(true);


switch(requestTypeOption){
    case ""Configure or Investigate SFTP"":
            // Showing/Requiring Report Type
            requestType.setHelpText(""This type is for updating existing sftp setup to configure folders and aliases, whitelist IP addresses, or for troubleshooting file syncing issues."");
            dueDate.setRequired(true);
                clientName.setRequired(true);
                linkToSfRecord.setRequired(true);
                clientCode.setRequired(true);
                partnerName.setRequired(false);
            link.setRequired(false);
            namingConvention.setRequired(false);
            frequency.setRequired(false);
            multitenant.setRequired(false);
            fileType.setRequired(false);
            useCase.setRequired(false);
            mappingDetails.setRequired(false);
            sftpDetails.setRequired(true);
            break;
    case ""Review and/or Automate New Data"":
            // Showing optional/required fields
            requestType.setHelpText(""This type is for reviewing and configuring any type of new data; eligibility, claims, tags, emails etc, for (automated) processing into our system."");
            dueDate.setRequired(true);
                clientName.setRequired(true);
                linkToSfRecord.setRequired(true);
                clientCode.setRequired(true);
                partnerName.setRequired(true);
            link.setRequired(true);
            namingConvention.setRequired(true);
            frequency.setRequired(true);
            multitenant.setRequired(true);
            fileType.setRequired(true);
            useCase.setRequired(true);
            mappingDetails.setRequired(true);
            sftpDetails.setRequired(true);
            break;
    case ""Review and/or Analyze Existing Data"":
            // Showing/Requiring Report Type
            requestType.setHelpText(""This type is for any questions, concerns, issues, investigations etc regarding the existing data in our files, systems or dashboards."");
            dueDate.setRequired(true);
                clientName.setRequired(true);
                linkToSfRecord.setRequired(true);
                clientCode.setRequired(true);
                partnerName.setRequired(false);
            link.setRequired(false);
            namingConvention.setRequired(false);
            frequency.setRequired(false);
            multitenant.setRequired(false);
            fileType.setRequired(false);
            useCase.setRequired(false);
            mappingDetails.setRequired(false);
            sftpDetails.setRequired(false);
            break;
    case ""Update Members"":
            // Showing optional/required fields
            requestType.setHelpText(""This type is for updating missing/incorrect values of existing tag/s or insurance information for members based on the recruitaible data."");
            dueDate.setRequired(true);
                clientName.setRequired(true);
                linkToSfRecord.setRequired(true);
                clientCode.setRequired(true);
                partnerName.setRequired(false);
            link.setRequired(false);
            namingConvention.setRequired(false);
            frequency.setRequired(false);
            multitenant.setRequired(false);
            fileType.setRequired(false);
            useCase.setRequired(false);
            mappingDetails.setRequired(true);
            sftpDetails.setRequired(false);
            break;
    case ""Stop Recruitable Data Feeds"": //hasta acá llegué
            // Showing optional/required fields
            requestType.setHelpText(""This type is for unconfiguring data feeds from being processed into our system and/or also deactivating corresponding sftp connections."");
            dueDate.setRequired(true);
                clientName.setRequired(true);
                linkToSfRecord.setRequired(true);
                clientCode.setRequired(true);
                partnerName.setRequired(false);
            link.setRequired(false);
            namingConvention.setRequired(false);
            frequency.setRequired(false);
            multitenant.setRequired(false);
            fileType.setRequired(false);
            useCase.setRequired(false);
            mappingDetails.setRequired(false);
            sftpDetails.setRequired(false);
            break;
    case ""Other"":
            requestType.setHelpText(""This type is for any other request that doesn't fall into any of the defined standard types."");
                dueDate.setRequired(true);
                clientName.setRequired(true);
                linkToSfRecord.setRequired(true);
                clientCode.setRequired(true);
                partnerName.setRequired(false);
            link.setRequired(false);
            namingConvention.setRequired(false);
            frequency.setRequired(false);
            multitenant.setRequired(false);
            fileType.setRequired(false);
            useCase.setRequired(false);
            mappingDetails.setRequired(false);
            sftpDetails.setRequired(false);
            break;
        case ""Other - Internal"":
            requestType.setHelpText(""This type is exclusively for internal DOPS projects and tasks and should only be created by a DOPS team member."");
                dueDate.setRequired(false);
                clientName.setRequired(false);
                linkToSfRecord.setRequired(false);
                clientCode.setRequired(false);
                partnerName.setRequired(false);
            link.setRequired(false);
            namingConvention.setRequired(false);
            frequency.setRequired(false);
            multitenant.setRequired(false);
            fileType.setRequired(false);
            useCase.setRequired(false);
            mappingDetails.setRequired(false);
            sftpDetails.setRequired(false);
            break;
    default:
                requestType.setHelpText("""");
                partnerName.setRequired(false);
                    link.setRequired(false);
                    namingConvention.setRequired(false);
                    frequency.setRequired(false);
                    multitenant.setRequired(false);
                    fileType.setRequired(false);
                    useCase.setRequired(false);
                    mappingDetails.setRequired(false);
                    sftpDetails.setRequired(false);
                break;
}
console.log(""Behaviours DATAOPS Project – Request Type completed."")","// This listens if the ""Request Type"" field has changed
if (getChangeField().getName() == ""Request Type"") {
    let requestType = getChangeField()

// This saves custom field's ids to manage its behaviors
let dueDate = getFieldById(""customfield_10090"") // Field (Due Date);
let clientName = getFieldById(""customfield_10090"") // Field (Client Name);
let linkToSfRecord = getFieldById(""customfield_10090"") // Field (Link to SF Record);
let clientCode = getFieldById(""customfield_10090"") // Field (Client Code);
let partnerName = getFieldById(""customfield_10090"") // Field (Partner Name);
let link = getFieldById(""customfield_10090"") // Field (Link);
let namingConvention = getFieldById(""customfield_10090"") // Field (Naming Convention);
let frequency = getFieldById(""customfield_10090"") // Field (Frequency);
let multitenant = getFieldById(""customfield_10090"") // Field (Multitenant);
let fileType = getFieldById(""customfield_10090"") // Field (File Type);
let useCase = getFieldById(""customfield_10090"") // Field (Use Case);
let mappingDetails = getFieldById(""customfield_10090"") // Field (Mapping Details);
let sftpDetails = getFieldById(""customfield_10090"") // Field (SFTP Details);

// This gets the value of ""Request Type"" as String
let requestTypeOption = requestType.getValue().value

switch(requestTypeOption){
    case ""Configure or Investigate SFTP"":
    // Showing/Requiring Report Type
        requestType.setDescription(""This type is for updating existing sftp setup to configure folders and aliases, whitelist IP addresses, or for troubleshooting file syncing issues."");
        dueDate.setRequired(true);
        clientName.setRequired(true);
        linkToSfRecord.setRequired(true);
        clientCode.setRequired(true);
        partnerName.setRequired(false);
        link.setRequired(false);
        namingConvention.setRequired(false);
        frequency.setRequired(false);
        multitenant.setRequired(false);
        fileType.setRequired(false);
        useCase.setRequired(false);
        mappingDetails.setRequired(false);
        sftpDetails.setRequired(true);
        break;
    case ""Review and/or Automate New Data"":
        // Showing optional/required fields
        requestType.setDescription(""This type is for reviewing and configuring any type of new data; eligibility, claims, tags, emails etc, for (automated) processing into our system."");
        dueDate.setRequired(true);
        clientName.setRequired(true);
        linkToSfRecord.setRequired(true);
        clientCode.setRequired(true);
        partnerName.setRequired(true);
        link.setRequired(true);
        namingConvention.setRequired(true);
        frequency.setRequired(true);
        multitenant.setRequired(true);
        fileType.setRequired(true);
        useCase.setRequired(true);
        mappingDetails.setRequired(true);
        sftpDetails.setRequired(true);
        break;
    case ""Review and/or Analyze Existing Data"":
        // Showing/Requiring Report Type
        requestType.setDescription(""This type is for any questions, concerns, issues, investigations etc regarding the existing data in our files, systems or dashboards."");
        dueDate.setRequired(true);
        clientName.setRequired(true);
        linkToSfRecord.setRequired(true);
        clientCode.setRequired(true);
        partnerName.setRequired(false);
        link.setRequired(false);
        namingConvention.setRequired(false);
        frequency.setRequired(false);
        multitenant.setRequired(false);
        fileType.setRequired(false);
        useCase.setRequired(false);
        mappingDetails.setRequired(false);
        sftpDetails.setRequired(false);
    break;
        case ""Update Members"":
        // Showing optional/required fields
        requestType.setDescription(""This type is for updating missing/incorrect values of existing tag/s or insurance information for members based on the recruitaible data."");
        dueDate.setRequired(true);
        clientName.setRequired(true);
        linkToSfRecord.setRequired(true);
        clientCode.setRequired(true);
        partnerName.setRequired(false);
        link.setRequired(false);
        namingConvention.setRequired(false);
        frequency.setRequired(false);
        multitenant.setRequired(false);
        fileType.setRequired(false);
        useCase.setRequired(false);
        mappingDetails.setRequired(true);
        sftpDetails.setRequired(false);
    break;
        case ""Stop Recruitable Data Feeds"": //hasta acá llegué
        // Showing optional/required fields
        requestType.setDescription(""This type is for unconfiguring data feeds from being processed into our system and/or also deactivating corresponding sftp connections."");
        dueDate.setRequired(true);
        clientName.setRequired(true);
        linkToSfRecord.setRequired(true);
        clientCode.setRequired(true);
        partnerName.setRequired(false);
        link.setRequired(false);
        namingConvention.setRequired(false);
        frequency.setRequired(false);
        multitenant.setRequired(false);
        fileType.setRequired(false);
        useCase.setRequired(false);
        mappingDetails.setRequired(false);
        sftpDetails.setRequired(false);
    break;
        case ""Other"":
        requestType.setDescription(""This type is for any other request that doesn't fall into any of the defined standard types."");
        dueDate.setRequired(true);
        clientName.setRequired(true);
        linkToSfRecord.setRequired(true);
        clientCode.setRequired(true);
        partnerName.setRequired(false);
        link.setRequired(false);
        namingConvention.setRequired(false);
        frequency.setRequired(false);
        multitenant.setRequired(false);
        fileType.setRequired(false);
        useCase.setRequired(false);
        mappingDetails.setRequired(false);
        sftpDetails.setRequired(false);
        break;
    case ""Other - Internal"":
        requestType.setDescription(""This type is exclusively for internal DOPS projects and tasks and should only be created by a DOPS team member."");
        dueDate.setRequired(false);
        clientName.setRequired(false);
        linkToSfRecord.setRequired(false);
        clientCode.setRequired(false);
        partnerName.setRequired(false);
        link.setRequired(false);
        namingConvention.setRequired(false);
        frequency.setRequired(false);
        multitenant.setRequired(false);
        fileType.setRequired(false);
        useCase.setRequired(false);
        mappingDetails.setRequired(false);
        sftpDetails.setRequired(false);
        break;
    default:
        requestType.setDescription("""");
        partnerName.setRequired(false);
        link.setRequired(false);
        namingConvention.setRequired(false);
        frequency.setRequired(false);
        multitenant.setRequired(false);
        fileType.setRequired(false);
        useCase.setRequired(false);
        mappingDetails.setRequired(false);
        sftpDetails.setRequired(false);
        break;
    }
}
"
18,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * COA project - Client Operations Associates
 * Issue Type: Task
 * Hide fields based on ""Type (SL)"" Question and set values for Report Type
 * Log: 12/28/2022 Eduardo Rojo (JIRA-6327)
 */
console.log(""Behaviours COA Project – Request Type started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Request Type"" field has changed
let typeSL = getFieldById(getFieldChanged());


// This gets the value of ""Request Type"" as String
let typeSLOption = typeSL.getValue() as String

let reportName = getFieldById(""Report Name"");
let groupId = getFieldById(""Group ID, Org ID or Client Code"");
let linkToSFRecord = getFieldById(""Link to SF Record"");
let programs = getFieldById(""Programs"");
let frequency = getFieldById(""Frequency"");


console.log(""Behaviours COA Project – Request Type: "" + typeSLOption);

switch(typeSLOption){
    case ""Standard Report Request"":
                typeSL.setDescription(""This report already exists in Sharepoint or Cognos."");
                reportName.setRequired(false); //change requested by Kelli Robinson. JIRA-6327 - Ignacio Vera 2/09/24
                groupId.setRequired(true);
                linkToSFRecord.setRequired(true);
                programs.setRequired(false); //change requested by Kelli Robinson. JIRA-6327 - Ignacio Vera 2/09/24
                programs.setVisible(true);
                frequency.setRequired(true);
            break;
    case ""Custom Report Request"":
                typeSL.setDescription(""This report does not exist anywhere and a custom report needs to be built."");
            reportName.setRequired(false);
                groupId.setRequired(true);
                linkToSFRecord.setRequired(true);
                programs.setRequired(false); //change requested by Kelli Robinson. JIRA-6327 - Ignacio Vera 2/09/24
                frequency.setRequired(true);
            break;
    case ""Ad-Hoc Report Request"":
                typeSL.setDescription("""");
            reportName.setRequired(false);
                groupId.setRequired(true);
                linkToSFRecord.setRequired(true);
                programs.setRequired(false); //change requested by Kelli Robinson. JIRA-6327 - Ignacio Vera 2/09/24
                frequency.setRequired(true);
            break;
    default:
        break;
}
console.log(""Behaviours COA Project – Request Type completed."")","if (getChangeField().getName() == ""Request Type"") {
    let typeSL = getChangeField();
        
    // This gets the value of ""Request Type"" as String
    let typeSLOption = typeSL.getValue().value

    let reportName = getFieldById(""customfield_10090"") // Field (Report Name);
    let groupId = getFieldById(""customfield_10090"") // Field (Group ID, Org ID or Client Code);
    let linkToSFRecord = getFieldById(""customfield_10090"") // Field (Link to SF Record);
    let programs = getFieldById(""customfield_10090"") // Field (Programs);
    let frequency = getFieldById(""customfield_10090"") // Field (Frequency);


    console.log(""Behaviours COA Project – Request Type: "" + typeSLOption);

    switch(typeSLOption){
        case ""Standard Report Request"":
            typeSL.setDescription(""This report already exists in Sharepoint or Cognos."");
            reportName.setRequired(false); //change requested by Kelli Robinson. JIRA-6327 - Ignacio Vera 2/09/24
            groupId.setRequired(true);
            linkToSFRecord.setRequired(true);
            programs.setRequired(false); //change requested by Kelli Robinson. JIRA-6327 - Ignacio Vera 2/09/24
            programs.setVisible(true);
            frequency.setRequired(true);
            break;
        case ""Custom Report Request"":
            typeSL.setDescription(""This report does not exist anywhere and a custom report needs to be built."");
            reportName.setRequired(false);
            groupId.setRequired(true);
            linkToSFRecord.setRequired(true);
            programs.setRequired(false); //change requested by Kelli Robinson. JIRA-6327 - Ignacio Vera 2/09/24
            frequency.setRequired(true);
            break;
        case ""Ad-Hoc Report Request"":
            typeSL.setDescription("""");
            reportName.setRequired(false);
            groupId.setRequired(true);
            linkToSFRecord.setRequired(true);
            programs.setRequired(false); //change requested by Kelli Robinson. JIRA-6327 - Ignacio Vera 2/09/24
            frequency.setRequired(true);
            break;
        default:
            break;
        }

}


"
19,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * COA project - Client Operations Associates
 * Hide fields based on ""Mode of Delivery"" value
 */

console.log(""Behaviours COA Project – Mode of Delivery started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager()
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Mode of Delivery"" field has changed
let modeOfDelivery = getFieldById(getFieldChanged());

// This saves the custom field ""SFTP - Client/Vendor Details"" and ""Email Delivery - Provide Email""
let sftpHostAndFolder = getFieldById(""SFTP Host and Folder"");
let emailDelivery = getFieldById(""Email Delivery - Provide Email"");

// This gets the value of ""Mode of Delivery"" field
let modeOfDeliveryOption = modeOfDelivery.getValue() as String

console.log(""Mode of Delivery: "" + modeOfDeliveryOption);
// Saves true/false if the ""Mode of Delivery"" option is match
let sftpHostAndFolderNew = modeOfDeliveryOption == ""SFTP""
//let vendorDetailsExisting = modeOfDeliveryOption == ""SFTP (Existing Connection)""
let emailDeliveryNew = modeOfDeliveryOption == ""Secure Email""

// Shows ""SFTP - Client/Vendor Details"" field when the option is ""SFTP""
if(sftpHostAndFolderNew){
    sftpHostAndFolder.setVisible(true);
	sftpHostAndFolder.setRequired(true);
}
else{
    sftpHostAndFolder.setVisible(false);
	sftpHostAndFolder.setRequired(false);
}
sftpHostAndFolder.setDescription("""");
emailDelivery.setDescription("""");

// // Shows ""Email Delivery - Provide Email"" field when the option is ""Email Delivery – For reports not containing PHI or PII""
emailDelivery.setVisible(!!emailDeliveryNew);
emailDelivery.setRequired(emailDeliveryNew);
console.log(""Behaviours COA Project – Mode of Delivery completed."")","if (getChangeField().getName() == ""Request Type"") {
    let typeSL = getChangeField();
        
    // This gets the value of ""Request Type"" as String
    let typeSLOption = typeSL.getValue().value

    let reportName = getFieldById(""customfield_10090"") // Field (Report Name);
    let groupId = getFieldById(""customfield_10090"") // Field (Group ID, Org ID or Client Code);
    let linkToSFRecord = getFieldById(""customfield_10090"") // Field (Link to SF Record);
    let programs = getFieldById(""customfield_10090"") // Field (Programs);
    let frequency = getFieldById(""customfield_10090"") // Field (Frequency);


    console.log(""Behaviours COA Project – Request Type: "" + typeSLOption);

    switch(typeSLOption){
        case ""Standard Report Request"":
            typeSL.setDescription(""This report already exists in Sharepoint or Cognos."");
            reportName.setRequired(false); //change requested by Kelli Robinson. JIRA-6327 - Ignacio Vera 2/09/24
            groupId.setRequired(true);
            linkToSFRecord.setRequired(true);
            programs.setRequired(false); //change requested by Kelli Robinson. JIRA-6327 - Ignacio Vera 2/09/24
            programs.setVisible(true);
            frequency.setRequired(true);
            break;
        case ""Custom Report Request"":
            typeSL.setDescription(""This report does not exist anywhere and a custom report needs to be built."");
            reportName.setRequired(false);
            groupId.setRequired(true);
            linkToSFRecord.setRequired(true);
            programs.setRequired(false); //change requested by Kelli Robinson. JIRA-6327 - Ignacio Vera 2/09/24
            frequency.setRequired(true);
            break;
        case ""Ad-Hoc Report Request"":
            typeSL.setDescription("""");
            reportName.setRequired(false);
            groupId.setRequired(true);
            linkToSFRecord.setRequired(true);
            programs.setRequired(false); //change requested by Kelli Robinson. JIRA-6327 - Ignacio Vera 2/09/24
            frequency.setRequired(true);
            break;
        default:
            break;
        }

}

"
20,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * COA project - Client Operations Associates
 * Issue Type: Task
 * Hide fields based on ""Request Type"" Question and set values for Report Type
 * Log: 12/28/2022 Eduardo Rojo (JIRA-6327)
 */
console.log(""Behaviours COA Project – Request Type started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

// This listens if the ""Request Type"" field has changed
let requestType = getFieldById(getFieldChanged());

// This saves custom field's ids to manage its behaviors
let typeSL = getFieldById(""Type (SL)"");

// This gets the value of ""Request Type"" as String
let requestTypeOption = requestType.getValue() as String

if(requestTypeOption==""Reporting Request""){
        typeSL.setRequired(true);
        typeSL.setVisible(true);
}
else{
        typeSL.setRequired(false);
        typeSL.setVisible(false);
}","

// This listens if the ""Request Type"" field has changed
if (getChangeField().getName() == ""Request Type"") {
    let requestType = getChangeField();

    // This saves custom field's ids to manage its behaviors
    let typeSL = getFieldById(""customfield_10090"") // Field (Type (SL));

    // This gets the value of ""Request Type"" as String
    let requestTypeOption = requestType.getValue().value

    if(requestTypeOption==""Reporting Request""){
            typeSL.setRequired(true);
            typeSL.setVisible(true);
    }
    else{
            typeSL.setRequired(false);
            typeSL.setVisible(false);
    }

}"
21,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Ignacio Vera
 * COA project - Client Operations Associates
 * Issue Type: Task
 * Hide/show fields based on ""Business Area"" field
 * Jira Ticket: JIRA-8730
 */
console.log(""Behaviours COA Project - Request Type started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

// This gets the value of ""Business Area"" as String
let businessArea = getFieldById(""Business Area"");
let businessAreaValue = businessArea.getValue() as String;

// Get customs fields hidden in Initialiser
let lineOfBusiness = getFieldById(""Channel - Line of Business"");
let clientName = getFieldById(""Client Name"");
let programsConditions = getFieldById(""Programs/Conditions"");

// Get required fields
let priorityField = getFieldById(""priority"");
let descriptionField = getFieldById(""Description"");
let reportName = getFieldById(""Report Name"");
let programsField = getFieldById(""Programs"");
let groupIdOrgField = getFieldById(""Group ID, Org ID or Client Code"");
let requestedDuedate = getFieldById(""Requested Due Date"");

// Get removable fields
let fileTypeExtension = getFieldById(""File Type Extension"");
let dateSpan = getFieldById(""Date Span"");


if(businessAreaValue==""USGH""){
	lineOfBusiness.setVisible(true); //show and make Channel - Line of Business required
   lineOfBusiness.setRequired(true);
   reportName.setVisible(false);     // Hide Report Name
   clientName.setVisible(true);    //Show and make Client Name required
   clientName.setRequired(true);
   programsField.setVisible(false); //Hide Programs field
   programsConditions.setVisible(true); //Show Programs/Conditions field and make it required.
 	programsConditions.setRequired(true);
   descriptionField.setRequired(true); //Make fields required:
   priorityField.setRequired(true);
 	groupIdOrgField.setRequired(true);
 	requestedDuedate.setRequired(true);
 	fileTypeExtension.setVisible(false); //Hide File Type Extension 
 	dateSpan.setVisible(false); //Hide Date Span 

}
else{
	reportName.setVisible(false); // Hide Report Name 
   clientName.setVisible(true); //Show and make Client Name required  
   clientName.setRequired(true); 
   descriptionField.setRequired(true); //Make fields required:
	priorityField.setRequired(true);
 	groupIdOrgField.setRequired(true);
 	requestedDuedate.setRequired(true);
   programsField.setVisible(false); //Hide Programs field
 	fileTypeExtension.setVisible(false); //Hide File Type Extension 
 	dateSpan.setVisible(false); //Hide Date Span 

}","// THIS CODE IS INCOMPLETE - THE FIELD BUSINESS AREA DIDN`T MIGRATE FROM DC
// This gets the value of ""Business Area"" as String
let businessArea = getFieldById(""customfield_10090"") // Field (Business Area); ! does not exist.
let businessAreaValue = businessArea.getValue() as String;

// Get customs fields hidden in Initialiser
let lineOfBusiness = getFieldById(""customfield_10090"") // Field (Channel - Line of Business);
let clientName = getFieldById(""customfield_10090"") // Field (Client Name);
let programsConditions = getFieldById(""customfield_10090"") // Field (Programs/Conditions);

// Get required fields
let priorityField = getFieldById(""customfield_10090"") // Field (priority);
let descriptionField = getFieldById(""customfield_10090"") // Field (Description);
let reportName = getFieldById(""customfield_10090"") // Field (Report Name);
let programsField = getFieldById(""customfield_10090"") // Field (Programs);
let groupIdOrgField = getFieldById(""customfield_10090"") // Field (Group ID, Org ID or Client Code);
let requestedDuedate = getFieldById(""customfield_10090"") // Field (Requested Due Date);

// Get removable fields
let fileTypeExtension = getFieldById(""customfield_10090"") // Field (File Type Extension);
let dateSpan = getFieldById(""customfield_10090"") // Field (Date Span);


if (businessAreaValue==""USGH""){
    lineOfBusiness.setVisible(true); //show and make Channel - Line of Business required
    lineOfBusiness.setRequired(true);
    reportName.setVisible(false);     // Hide Report Name
    clientName.setVisible(true);    //Show and make Client Name required
    clientName.setRequired(true);
    programsField.setVisible(false); //Hide Programs field
    programsConditions.setVisible(true); //Show Programs/Conditions field and make it required.
    programsConditions.setRequired(true);
    descriptionField.setRequired(true); //Make fields required:
    priorityField.setRequired(true);
    groupIdOrgField.setRequired(true);
    requestedDuedate.setRequired(true);
    fileTypeExtension.setVisible(false); //Hide File Type Extension 
    dateSpan.setVisible(false); //Hide Date Span 
}
else {
    reportName.setVisible(false); // Hide Report Name 
    clientName.setVisible(true); //Show and make Client Name required  
    clientName.setRequired(true); 
    descriptionField.setRequired(true); //Make fields required:
    priorityField.setRequired(true);
    groupIdOrgField.setRequired(true);
    requestedDuedate.setRequired(true);
    programsField.setVisible(false); //Hide Programs field
    fileTypeExtension.setVisible(false); //Hide File Type Extension 
    dateSpan.setVisible(false); //Hide Date Span 

}

"
22,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: RC-Platform field -> Populate RC-Platform Owner
Change log: ER 10-11-2024 JIRA-10050
            ER 11-112024 JIRA-10169
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours
final projectName = issueContext?.projectObject?.name
final issueTypeName = issueContext?.getIssueType()?.getName()
final loggedInUser = ComponentAccessor?.jiraAuthenticationContext?.loggedInUser
final rcPlatformOwner = underlyingIssue?.getCustomFieldValue(""RC-Platform Owner"")
final origRCPlatform = underlyingIssue?.getCustomFieldValue(""RC-Platform"")
let origRCPlatformValue = origRCPlatform?.getValue()  //.getValue()s() as HashMap
let rcPlatform = getFieldById(getFieldChanged())
let rcPlatformValue = rcPlatform?.getValue()
log.debug(""rcPlatformValue: ${rcPlatformValue}"")
log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")
if (getAction()?.id == null || !(getAction()?.id == 1 )){
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field **EDIT & VIEW Screens**."")
    //If someone changes Platform, we need to update the Platform Owner also
    if(origRCPlatformValue?.toString() != rcPlatformValue?.toString()) {
        populateChangeOwner()
    }
}else{ //This is the initial Create Action
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field  **Create Action**."")
    populateChangeOwner()
}
log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions
let populateChangeOwner() {
    //let platform = underlyingIssue?.getCustomFieldValue(""Platform"")
    let rcPlatform = getFieldById(""RC-Platform"")
    let rcPlatformValue = rcPlatform?.getValue() as String
    log.debug(""rcPlatformValue: ${rcPlatformValue}"")
    if(rcPlatformValue == null || rcPlatformValue == """"){return ""RC Platform is empty.""}
    let rcPlatformOwner = getFieldById(""RC-Platform Owner"")
    switch(rcPlatformValue?.toLowerCase()){
        case ""clinical usgh"": // updated ""clinical"" to ""clinical usgh"" JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""consumer experience"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""corporate applications"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""cybersecurity"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""data"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""devops engineering"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""ecosystem"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;        
        case ""engagement"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs engineering"": // added JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs platform ops"": // added JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs technical operations"": // added JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""marketing operations"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""member support"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""product management"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;        
        case ""service, benefit, and client"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""solutions delivery"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""supply chain"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;        
        case ""tdh international"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""technology services"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""technical operations"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""workplace services"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        //Adding the new value JIRA-10169
        case ""hhs solution analysis and design"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        default:
            rcPlatformOwner?.setValue("""")
            rcPlatformOwner?.setReadOnly(false)
        break;

    }
}",
23,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: CA-Platform field -> Populate CA-Platform Owner
Change log: ER 10-11-2024 JIRA-10050
            ER 11-112024 JIRA-10169
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours
final projectName = issueContext?.projectObject.name
final issueTypeName = issueContext?.getIssueType()?.getName()
final loggedInUser = ComponentAccessor?.jiraAuthenticationContext?.loggedInUser
final caPlatformOwner = underlyingIssue?.getCustomFieldValue(""CA-Platform Owner"")
final origCAPlatform = underlyingIssue?.getCustomFieldValue(""CA-Platform"")
let origCAPlatformValue = origCAPlatform?.getValue()  //.getValue()s() as HashMap
let caPlatform = getFieldById(getFieldChanged())
let caPlatformValue = caPlatform?.getValue()

log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")
if (getAction()?.id == null || !(getAction()?.id == 1 )){
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field **EDIT & VIEW Screens**."")
    //If someone changes CA-Platform, we need to update the CA-Platform Owner also
    if(origCAPlatformValue?.toString() != caPlatformValue?.toString()) {
        populateChangeOwner()
    }
}else{ //This is the initial Create Action
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field  **Create Action**."")
    populateChangeOwner()
}
log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions
let populateChangeOwner() {
    //let platform = underlyingIssue?.getCustomFieldValue(""Platform"")
    let caPlatform = getFieldById(""CA-Platform"")
    let caPlatformValue = caPlatform?.getValue() as String
    if(caPlatformValue == null || caPlatformValue == """"){return ""RC Platform is empty.""}
    let caPlatformOwner = getFieldById(""CA-Platform Owner"")
    switch(caPlatformValue?.toLowerCase()){
        case ""clinical usgh"": // updated ""clinical"" to ""clinical usgh"" JIRA-10050
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""consumer experience"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""corporate applications"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""cybersecurity"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""data"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""devops engineering"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""ecosystem"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""engagement"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs engineering"": // added JIRA-10050
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs platform ops"": // added JIRA-10050
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs technical operations"": // added JIRA-10050
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""marketing operations"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""member support"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""product management"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""service, benefit, and client"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""solutions delivery"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""supply chain"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""tdh international"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""technical operations"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""technology services"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""workplace services"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        //Adding the new value JIRA-10169
        case ""hhs solution analysis and design"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        default:
            caPlatformOwner?.setValue("""")
            caPlatformOwner?.setReadOnly(false)
        break;
    }
}",
24,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: Impacted Countries = Other display 'Other' field
Change log:
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours

let impactedCountries = getFieldById(getFieldChanged())
String impactedCountriesValue = impactedCountries?.getValue().toString().replaceAll(""\\["", """").replaceAll(""\\]"","""")
let other = getFieldById(""Other"")
other?.setVisible(false).setRequired(false)
log.debug(""impactedCountriesValue: $impactedCountriesValue}"")

if(impactedCountriesValue.includes('Other')){  //} == 'Other'){
    log.debug(""impactedCountriesValue-1: $impactedCountriesValue}"")
    other?.setVisible(true).setRequired(true)
    other?.setDescription(""For other countries not available in the dropdown."")
}",
25,"// Had Imports
// com.atlassian.jira.issue.customfields.option.Options
// com.atlassian.jira.issue.fields.option.Option
// com.atlassian.jira.component.ComponentAccessor
// com.onresolve.jira.groovy.user.FieldBehaviours
// com.atlassian.jira.issue.Issue
// com.atlassian.crowd.embedded.api.Group
// com.onresolve.scriptrunner.parameters.annotation.GroupPicker
// com.atlassian.jira.security.roles.ProjectRoleManager
// groovy.transform.BaseScript
// org.apache.log4j.Level
// org.apache.log4j.Logger
/*
Creator: Jeff Melies
Purpose: Set Priority depending on selection of both Impact & Urgency
Change log:
*/

log.setLevel(Level.DEBUG)

@BaseScript FieldBehaviours fieldBehaviours
//Managers
let groupManager = ComponentAccessor.groupManager
let customFieldManager = ComponentAccessor.getCustomFieldManager()
let optionsManager = ComponentAccessor.getOptionsManager()
// Rewritten / ProjectRoleManager Substitution
let context = await getContext()
let projectKey = context.extension.project.key
let resMyRolesInProject = await makeRequest(`/rest/api/2/project/${projectKey}/roledetails?currentMember=True`)
let myRolesInProject = resMyRolesInProject.body            
              

Issue issue = underlyingIssue
final projectName = issueContext.projectObject.name
final issueTypeName = issueContext.getIssueType().getName()

log = Logger.getLogger(""${issue}: Behaviours: ${projectName}- Field: Urgency"") 

final origUrgency = issue?.getCustomFieldValue(""Urgency"") //Urgency only after ticket has been created
let urgency = getFieldById(getFieldChanged()) //Urgency during create and if field has/is changed
let impact = getFieldById(""Impact"")
//let changeType = getFieldById(""Change type"")
//Map changeTypeValue = changeType?.getValue() as Map
//final roleName = 'Administrators'
//let projectRole = projectRoleManager.getProjectRole(roleName)
//let roleActors = projectRoleManager.getProjectRoleActors(projectRole, issueContext.projectObject).users*.emailAddress
//final loggedInUser = ComponentAccessor.jiraAuthenticationContext.loggedInUser

//Always do these items:
log.debug(""Has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")
if(!(issue?.getCustomFieldValue(""Urgency"") || issue?.getCustomFieldValue(""Impact""))){
    log.debug(""Urgency or Impact IS NULL, EXITING EARLY."")
    return
}

if (getAction()?.id == null || !(getAction()?.id == 1 )){
    log.debug(""**EDIT & VIEW Screens**"")
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
//    fieldSetup()
}else{ //This is the initial Create Action
    log.debug(""**Create Action**"")
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
    if(urgency?.getValue().toString() != null || urgency?.getValue().toString() != ''){
        log.debug(""Urgency is NOT null: ${urgency?.getValue().toString()}, origUrgency: ${origUrgency?.getValue().toString()}"")
//        fieldSetup()
    }else{
        log.debug(""Urgency is null: ${urgency?.getValue().toString()}, origUrgency: ${origUrgency?.getValue().toString()}"")
    }
}

log.debug(""Has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions below here
/*
let fieldSetup(){
    final projectName = issueContext.projectObject.name
    final issueTypeName = issueContext.getIssueType().getName()
    let urgency = getFieldById(""Urgency"")
    let impact = getFieldById(""Impact"")
    //let changeType = getFieldById(""Change type"")
    //Map changeTypeValue = changeType.getValue() as Map

    //switch(changeTypeValue[0].toString().toLowerCase()){
    //    case ""standard"":
            if(urgency?.getValue().toString().toLowerCase() == 'medium'){
                log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Urgency Field' - SWITCH: Medium"")
    //            getFieldById(""Urgency"").setDescription('Non-scripted / non-automated Change that is manually configured/ deployed. - A Change that has a feasible roll back mechanism that has been tested and validated to work in a timely manor. - Software/ Code Deployments Subcategory to production systems. - Software/ Code Deployments to high profile CAT/ UAT systems.')
                List slOptions = [""Minor / Localized""]
                selectListOptions(""Impact"", slOptions)
                impact.setVisible(true).setAllowInlineEdit(false)
                impact.setValue(""Minor / Localized"")
                impact.setReadOnly(false).setAllowInlineEdit(false)
            }else{
                log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Risk Field' - SWITCH: Standard/Not Medium."")
                getFieldById(""Risk"").setDescription('The deployment process is for a non- production system. - The Change deployment method is standardized with the use of well documented procedures with low risk of failure. - The majority of the Change is deployed through scripted means that are well tested and known to work when performing the Change to non- production systems. - The Change is deployed via a vendor compiled installation application that has been tested and known to work with the targeted environment/ purpose for non- production systems.')
                List slOptions = [""Moderate / Limited"", ""Minor / Localized""]
                selectListOptions(""Impact"", slOptions)
                impact.setVisible(true).setAllowInlineEdit(false)
                impact.setReadOnly(false).setAllowInlineEdit(false)               
            }
        break;
        case ""cab"":
            switch(risk.getValue().toString().toLowerCase())
            {
                case ""low"":
                    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Risk Field' - SWITCH: CAB/Low."")
                    getFieldById(""Risk"").setDescription('The deployment process is for a non- production system. - The Change deployment method is standardized with the use of well documented procedures with low risk of failure. - The majority of the Change is deployed through scripted means that are well tested and known to work when performing the Change to non- production systems. - The Change is deployed via a vendor compiled installation application that has been tested and known to work with the targeted environment/ purpose for non- production systems.')
                    List slOptions = [""Significant / Large""]
                    selectListOptions(""Impact"", slOptions)
                    impact.setVisible(true).setReadOnly(false).setAllowInlineEdit(false) 
                    impact.setValue(""Significant / Large"")
                break;
                case ""medium"":
                    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Risk Field' - SWITCH: CAB/Medium."")
                    getFieldById(""Risk"").setDescription('Non-scripted / non-automated Change that is manually configured/ deployed. - A Change that has a feasible roll back mechanism that has been tested and validated to work in a timely manor. - Software/ Code Deployments Subcategory to production systems. - Software/ Code Deployments to high profile CAT/ UAT systems.')
                    List slOptions = [""Moderate / Limited"", ""Significant / Large""]
                    selectListOptions(""Impact"", slOptions)
                    impact.setVisible(true).setReadOnly(false).setAllowInlineEdit(false) 
                break;
                case ""high"":
                    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Risk Field' - SWITCH: CAB/High."")
                    getFieldById(""Risk"").setDescription('Adding new hardware or software components (network, application, database, storage) to a major system already in use in production. - Risk of failure could impact systems that put patients at risk. - Change failure could cause an outage of mission critical system(s). - Change can take out a customer facing system and would require significant time, or resources, to bring the system back online. - Performing a Change that cannot be rolled back in a timely manor, or not rolled back at all. - Deploying the Change, or rolling it back, requires close coordination and effort across multiple teams.')
                    List slOptions = [""Minor / Localized"",""Moderate / Limited"", ""Significant / Large""]
                    selectListOptions(""Impact"", slOptions)
                    impact.setVisible(true).setReadOnly(false).setAllowInlineEdit(false) 
                break;
                case ""critical"":
                    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Risk Field' - SWITCH: CAB/Critical."")
                    List slOptions = [""Minor / Localized"",""Moderate / Limited"", ""Significant / Large""]
                    selectListOptions(""Impact"", slOptions)
                    impact.setVisible(true).setReadOnly(false).setAllowInlineEdit(false) 
                break;                
            }
        break;
        case ""expedite"":
            switch(risk.getValue().toString().toLowerCase())
            {
                case ""low"":
                    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Risk Field' - SWITCH: Expedite/Low."")
                    getFieldById(""Risk"").setDescription('The deployment process is for a non- production system. - The Change deployment method is standardized with the use of well documented procedures with low risk of failure. - The majority of the Change is deployed through scripted means that are well tested and known to work when performing the Change to non- production systems. - The Change is deployed via a vendor compiled installation application that has been tested and known to work with the targeted environment/ purpose for non- production systems.')
                    List slOptions = [""Significant / Large""]
                    selectListOptions(""Impact"", slOptions)
                    impact.setVisible(true).setReadOnly(false).setAllowInlineEdit(false) 
                    impact.setValue(""Significant / Large"")
                break;
                case ""medium"":
                    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Risk Field' - SWITCH: Expedite/Medium."")
                    getFieldById(""Risk"").setDescription('Non-scripted / non-automated Change that is manually configured/ deployed. - A Change that has a feasible roll back mechanism that has been tested and validated to work in a timely manor. - Software/ Code Deployments Subcategory to production systems. - Software/ Code Deployments to high profile CAT/ UAT systems.')                    
                    List slOptions = [""Moderate / Limited"", ""Significant / Large""]
                    selectListOptions(""Impact"", slOptions)
                    impact.setVisible(true).setReadOnly(false).setAllowInlineEdit(false) 
                break;
                case ""high"":
                    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Risk Field' - SWITCH: Expedite/High."")
                    getFieldById(""Risk"").setDescription('Adding new hardware or software components (network, application, database, storage) to a major system already in use in production. - Risk of failure could impact systems that put patients at risk. - Change failure could cause an outage of mission critical system(s). - Change can take out a customer facing system and would require significant time, or resources, to bring the system back online. - Performing a Change that cannot be rolled back in a timely manor, or not rolled back at all. - Deploying the Change, or rolling it back, requires close coordination and effort across multiple teams.')
                    List slOptions = [""Minor / Localized"",""Moderate / Limited"", ""Significant / Large""]
                    selectListOptions(""Impact"", slOptions)
                    impact.setVisible(true).setReadOnly(false).setAllowInlineEdit(false) 
                break;
            }
        break;
        case ""emergency"":
            log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Risk Field' - SWITCH: Emergency."")
            List slOptions = [""Minor / Localized"",""Moderate / Limited"", ""Significant / Large""]
            selectListOptions(""Impact"", slOptions)
            impact.setVisible(true).setReadOnly(false).setAllowInlineEdit(false) 
            switch(risk.getValue().toString().toLowerCase())
            {
                case ""low"":
                    getFieldById(""Risk"").setDescription('The deployment process is for a non- production system. - The Change deployment method is standardized with the use of well documented procedures with low risk of failure. - The majority of the Change is deployed through scripted means that are well tested and known to work when performing the Change to non- production systems. - The Change is deployed via a vendor compiled installation application that has been tested and known to work with the targeted environment/ purpose for non- production systems.')
                break;
                case ""medium"":
                    getFieldById(""Risk"").setDescription('Non-scripted / non-automated Change that is manually configured/ deployed. - A Change that has a feasible roll back mechanism that has been tested and validated to work in a timely manor. - Software/ Code Deployments Subcategory to production systems. - Software/ Code Deployments to high profile CAT/ UAT systems.')
                break;
                case ""high"":
                    getFieldById(""Risk"").setDescription('Adding new hardware or software components (network, application, database, storage) to a major system already in use in production. - Risk of failure could impact systems that put patients at risk. - Change failure could cause an outage of mission critical system(s). - Change can take out a customer facing system and would require significant time, or resources, to bring the system back online. - Performing a Change that cannot be rolled back in a timely manor, or not rolled back at all. - Deploying the Change, or rolling it back, requires close coordination and effort across multiple teams.')
                break;
                default:
                break;
            }
        break;
        default:
            log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Risk Field' - ********** DEFAULT ********THIS NEEDS TO BE FIXED.******"")
        break;

    }
}

let selectListOptions(String cfName, List availableOpt){
    let cf = getFieldById(cfName) 
    let cfObj = customFieldManager.getCustomFieldObject(cf.getFieldId())
    let cfConfig = cfObj.getRelevantConfig(getIssueContext())
    let cfOptions = ComponentAccessor.getOptionsManager().getOptions(cfConfig)

    let cfA = cfOptions.filter( 
        it.getValue() in availableOpt as List }.collectEntries {
            [ (it.optionId.toString()) : it.getValue() ] } as Map
    cf.setOptionsVisibility(cfA)
}
*/
",Pay Attention to options
26,"// Had Imports
// com.atlassian.jira.user.ApplicationUser
// com.atlassian.jira.issue.IssueInputParameters
// com.atlassian.jira.issue.priority.Priority
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.event.type.EventDispatchOption
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// com.atlassian.jira.security.roles.ProjectRoleManager
// groovy.transform.BaseScript
// org.apache.log4j.Logger
// org.apache.log4j.Level
//Creator: Jeff Melies
//Purpose: Set Priority depending on selection of both Impact & Urgency
//Change log:

log.setLevel(Level.DEBUG)

Issue issue = underlyingIssue

@BaseScript FieldBehaviours fieldBehaviours
//Managers
// Rewritten / ProjectRoleManager Substitution
let context = await getContext()
let projectKey = context.extension.project.key
let resMyRolesInProject = await makeRequest(`/rest/api/2/project/${projectKey}/roledetails?currentMember=True`)
let myRolesInProject = resMyRolesInProject.body            
              
let customFieldManager = ComponentAccessor.getCustomFieldManager()
let optionsManager = ComponentAccessor.getOptionsManager()

final projectName = issueContext.projectObject.name
log = Logger.getLogger(""${issue}: Behaviours: ${projectName}-Field: Impact"") 

final issueTypeName = issueContext.getIssueType().getName()
let impact = getFieldById(getFieldChanged()) //Impact
String origImpact = issue?.getCustomFieldValue(""Impact"").toString()  //Original Impact value
String urgencyValue = issue?.getCustomFieldValue(""Urgency"").toString()
//getFieldById(""Priority"").setReadOnly(true)

if (!(origImpact || urgencyValue)){
    log.debug(""'Impact' or 'Urgency' field is empty EXIT EARLY"")
    return
}

log.debug(""Has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

if (getAction()?.id == null || !(getAction()?.id == 1 )){
    log.debug(""**EDIT & VIEW Screens**"")
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
    //If someone changes the Impact field do the following
//    fieldSetup()               *******************ENABLE THIS TO GET THE AUTOMATION TO WORK******
}else{ //This is the initial Create Action
    log.debug(""**Create action**"")
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
//    fieldSetup()               *******************ENABLE THIS TO GET THE AUTOMATION TO WORK******
}
log.debug(""Has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions
let fieldSetup(){
    let issueService = ComponentAccessor.issueService
    Issue issue = underlyingIssue
    String impactValue = getFieldById(""Impact"")?.getValue().toString().toLowerCase()
    String urgencyValue = getFieldById(""urgency"")?.getValue().toString().toLowerCase()
    let availablePriorities = ComponentAccessor.constantsManager.priorities
    Priority newPriority
    let pr
    switch(impactValue){
        case ""high (extensive/widespread)"":
            if(urgencyValue == ""high (immediately)""){
                newPriority = availablePriorities.find { it.name == ""P1"" }
            }else if(urgencyValue == ""medium (hours)""){
                newPriority = availablePriorities.find { it.name == ""P2"" }
            }else{
                newPriority = availablePriorities.find { it.name == ""P3"" }
            }
        break;
        case ""medium (significant/large)"":
            if(urgencyValue == ""high (immediately)""){
                newPriority = availablePriorities.find { it.name == ""P2"" }
            }else if(urgencyValue == ""medium (hours)""){
                newPriority = availablePriorities.find { it.name == ""P3"" }
            }else{
                newPriority = availablePriorities.find { it.name == ""P4"" }
            }
        break;
        case ""low (minor)"":
            if(urgencyValue == ""high (immediately)""){
                newPriority = availablePriorities.find { it.name == ""P3"" }
            }else if(urgencyValue == ""medium (hours)""){
                newPriority = availablePriorities.find { it.name == ""P4"" }
            }else{
                newPriority = availablePriorities.find { it.name == ""P5"" }
            }
        break;
        default:
            getFieldById(""Priority"").setVisible(true).setReadOnly(true)
            newPriority = null
        break;
    }
    assert newPriority : ""Could not find priority name. Available priorities are ${availablePriorities*.name.join("", "")}""
    let issueInputParams = issueService.newIssueInputParameters() as IssueInputParameters
    issueInputParams.with {
        priorityId = newPriority.id
    }

    let updateValidationResult = issueService.validateUpdate(ComponentAccessor.jiraAuthenticationContext.loggedInUser, issue?.id, issueInputParams)
    assert updateValidationResult.valid : updateValidationResult.errorCollection

    let updateResult = issueService.update(ComponentAccessor.jiraAuthenticationContext.loggedInUser, updateValidationResult, EventDispatchOption.DO_NOT_DISPATCH, false)
    assert updateResult.valid : updateResult.errorCollection

}",
27,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: Platform field -> Populate Platform Owner
Change log:
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours
final projectName = issueContext.projectObject.name
final issueTypeName = issueContext.getIssueType().getName()
final loggedInUser = ComponentAccessor.jiraAuthenticationContext.loggedInUser
final platformOwner = underlyingIssue?.getCustomFieldValue(""Platform Owner"")
final origPlatform = underlyingIssue?.getCustomFieldValue(""Platform"")
let origPlatformValue = origPlatform?.getValue()  //.getValue()s() as HashMap
let platform = getFieldById(getFieldChanged())
let platformValue = platform?.getValue()

log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Platform' Field has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")
if (getAction()?.id == null || !(getAction()?.id == 1 )){
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Platform' Field **EDIT & VIEW Screens**."")
    //If someone changes CA-Platform, we need to update the Platform Owner also
    if(origPlatformValue.toString() != platformValue.toString()) {
        populateChangeOwner()
    }
}else{ //This is the initial Create Action
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Platform' Field  **Create Action**."")
    populateChangeOwner()
}
log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'Platform' Field has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions
let populateChangeOwner() {
    //let platform = underlyingIssue?.getCustomFieldValue(""Platform"")
    let platform = getFieldById(""Platform"")
    let platformValue = platform.getValue() as String
    let platformOwner = getFieldById(""Platform Owner"")
    switch(platformValue.toLowerCase()){
        case ""clinical"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""consumer"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""corporate applications"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""cybersecurity"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""data"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""devops"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""marketing"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""member support"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""service & client"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""solutions delivery"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""tdh international"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""technology services"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""technical operations (coe)"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        case ""workplace services"":
            platformOwner.setValue(""<EMAIL>"")
            platformOwner.setReadOnly(true)
        break;
        default:
            platformOwner.setValue("""")
            platformOwner.setReadOnly(false)
        break;

    }
}",
28,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: RC-Platform field -> Populate RC-Platform Owner
Change log:
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours
final projectName = issueContext.projectObject.name
final issueTypeName = issueContext.getIssueType()?.getName()
final loggedInUser = ComponentAccessor.jiraAuthenticationContext.loggedInUser
final rcPlatformOwner = underlyingIssue?.getCustomFieldValue(""RC-Platform Owner"")
final origRCPlatform = underlyingIssue?.getCustomFieldValue(""RC-Platform"")
let origRCPlatformValue = origRCPlatform?.getValue()  //.getValue()s() as HashMap
let rcPlatform = getFieldById(getFieldChanged())
let rcPlatformValue = rcPlatform?.getValue()

log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")
if (getAction()?.id == null || !(getAction()?.id == 1 )){
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field **EDIT & VIEW Screens**."")
    //If someone changes Platform, we need to update the Platform Owner also
    if(origRCPlatformValue.toString() != rcPlatformValue.toString()) {
        populateChangeOwner()
    }
}else{ //This is the initial Create Action
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field  **Create Action**."")
    populateChangeOwner()
}
log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'RC-Platform' Field has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions
let populateChangeOwner() {
    //let platform = underlyingIssue?.getCustomFieldValue(""Platform"")
    let rcPlatform = getFieldById(""RC-Platform"")
    let rcPlatformValue = rcPlatform.getValue() as String
    let rcPlatformOwner = getFieldById(""RC-Platform Owner"")
    switch(rcPlatformValue?.toLowerCase()){
       case ""clinical usgh"": // updated ""clinical"" to ""clinical usgh"" JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""consumer experience"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""corporate applications"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""cybersecurity"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""data"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""devops engineering"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""ecosystem"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;        
        case ""engagement"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs engineering"": // added JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs platform ops"": // added JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""hhs technical operations"": // added JIRA-10050
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""marketing operations"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""member support"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""product management"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;        
        case ""service, benefit, and client"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""solutions delivery"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""supply chain"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;        
        case ""tdh international"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""technology services"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""technical operations"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        case ""workplace services"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        //Adding the new value JIRA-10169
        case ""hhs solution analysis and design"":
            rcPlatformOwner?.setValue(""<EMAIL>"")
            rcPlatformOwner?.setReadOnly(true)
        break;
        default:
            rcPlatformOwner?.setValue("""")
            rcPlatformOwner?.setReadOnly(false)
        break;
    }
}",
29,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: CA-Platform field -> Populate CA-Platform Owner
Change log:
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours
final projectName = issueContext.projectObject.name
final issueTypeName = issueContext.getIssueType()?.getName()
final loggedInUser = ComponentAccessor.jiraAuthenticationContext.loggedInUser
final caPlatformOwner = underlyingIssue?.getCustomFieldValue(""CA-Platform Owner"")
final origCAPlatform = underlyingIssue?.getCustomFieldValue(""CA-Platform"")
let origCAPlatformValue = origCAPlatform?.getValue()  //.getValue()s() as HashMap
let caPlatform = getFieldById(getFieldChanged())
let caPlatformValue = caPlatform?.getValue()

log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field has started on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")
if (getAction()?.id == null || !(getAction()?.id == 1 )){
    //*******************************************************
    //*******Do this on Edit and View screens**************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field **EDIT & VIEW Screens**."")
    //If someone changes CA-Platform, we need to update the CA-Platform Owner also
    if(origCAPlatformValue.toString() != caPlatformValue.toString()) {
        populateChangeOwner()
    }
}else{ //This is the initial Create Action
    //*******************************************************
    //*******Do this on create screen only*******************
    //*******************************************************
    log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field  **Create Action**."")
    populateChangeOwner()
}
log.debug(""Behaviours: ${projectName}, ${issueTypeName}-'CA-Platform' Field has completed on ${if(underlyingIssue){underlyingIssue?.getKey()}else{'a New Issue'}}."")

//Functions
let populateChangeOwner() {
    //let platform = underlyingIssue?.getCustomFieldValue(""Platform"")
    let caPlatform = getFieldById(""CA-Platform"")
    let caPlatformValue = caPlatform?.getValue() as String
    let caPlatformOwner = getFieldById(""CA-Platform Owner"")
    switch(caPlatformValue?.toLowerCase()){
        case ""clinical"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""consumer experience"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""corporate applications"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""cybersecurity"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""data"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""devops engineering"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""ecosystem"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""engagement"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""marketing operations"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""member support"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""product management"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""service, benefit, and client"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""solutions delivery"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""supply chain"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""tdh international"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""technical operations"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""technology services"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        case ""workplace services"":
            caPlatformOwner?.setValue(""<EMAIL>"")
            caPlatformOwner?.setReadOnly(true)
        break;
        default:
            caPlatformOwner?.setValue("""")
            caPlatformOwner?.setReadOnly(false)
        break;
    }
}",
30,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.Issue
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// org.apache.log4j.Level
/*
Creator: Jeff Melies
Purpose: Impacted Countries = Other display 'Other' field
Change log:
*/
//imports
log.setLevel(Level.INFO)

Issue issue = UnderlyingIssue

@BaseScript FieldBehaviours fieldBehaviours

let impactedCountries = getFieldById(getFieldChanged())
String impactedCountriesValue = impactedCountries?.getValue().toString()
let other = getFieldById(""Other"")
other.setVisible(false).setRequired(false)

if(impactedCountriesValue.includes('Other')){  //} == 'Other'){ == 'Other'){
    other.setVisible(true).setRequired(true)
}",
31,"
// Checks if the ""Category"" field has been changed
let changedFieldValue = getFieldById(getFieldChanged()).getValue()
let description = getFieldById(""Description"")

console.log(""Category changed to: '$changedFieldValue'"")

let actionName = getActionName()
console.log(""Action was: '$actionName'"")

if (getActionName() in [""Create Issue"",""Create""]) {

    if (changedFieldValue == ""New SSO configuration request"") {

        let strDescription = """"""
        *Customer Name:*

        *Org ID* (if applicable):

        *Group ID* (if applicable):

        *Eligibility Type* (Example: Eligibility File or RTE or Hybrid):

        *Payer:*

        *Contact Information - Please provide the best point of contact for the following:*
        * Coordinating the implementation and testing phases
        * Troubleshooting any production issues after implementation
        * Updating certificates

        *Timeline/Expected Go Live Date* (NOTE: New SSO requests take 4-6 weeks. It can be faster based on information gathering):

        *Additional Details:*

        *NOTE:*  _(These links only become clickable once the ticket has been created)_ Please provide [Technical Specifications Overview.docx|https://teladocpa.sharepoint.com/:w:/r/sites/Solution/Shared%20Documents/General/Integrations/USGH/SSO/Client%20Documents/Technical%20Specifications%20Overview.docx?d=wa0b6a116745c40af91423483614e46f7&csf=1&web=1&e=SoGAad] and [Teladoc Single Sign On Business Requirement Document Template.docx|https://teladocpa.sharepoint.com/:w:/r/sites/Solution/Shared%20Documents/General/Integrations/USGH/SSO/Client%20Documents/Teladoc%20Single%20Sign%20On%20Business%20Requirement%20Document%20Template.docx?d=w724f66f9b8954e6f9ee057160bc1f75a&csf=1&web=1&e=0iRzaQ] to new SSO client. 
        * If you cannot open above files, move your mouse cursor to [Technical Specifications Overview.docx|https://teladocpa.sharepoint.com/:w:/r/sites/Solution/Shared%20Documents/General/Integrations/USGH/SSO/Client%20Documents/Technical%20Specifications%20Overview.docx?d=wa0b6a116745c40af91423483614e46f7&csf=1&web=1&e=SoGAad] or [Teladoc Single Sign On Business Requirement Document Template.docx|https://teladocpa.sharepoint.com/:w:/r/sites/Solution/Shared%20Documents/General/Integrations/USGH/SSO/Client%20Documents/Teladoc%20Single%20Sign%20On%20Business%20Requirement%20Document%20Template.docx?d=w724f66f9b8954e6f9ee057160bc1f75a&csf=1&web=1&e=0iRzaQ]. Click right mouse cursor and click open in new tab. You will be able to see those documents. Please get back to this ticket when you get completed Teladoc Single Sign On Business Requirement Document word file from a client.
        """"""

        description.setValue(strDescription)
    } else if (changedFieldValue == ""Existing SSO configuration (Adding or removing groups) request"") {

        let strDescription = """"""
        *Customer Name:*  

        *Issuer/Entity ID:*  

        *Adding or Removing SSO:*  

        *Org ID* (if applicable):  

        *Group ID* (if applicable): 

        *Eligibility Type* (Example: Eligibility File or RTE or Hybrid):

        *Contact Information - Please provide the best point of contact for the following:*
        * (Adding groups only) *testing phases and troubleshooting any production issues*

        *Expected Due Date:*  

        *Additional Details:*
        """"""

        description.setValue(strDescription)
    } else if (changedFieldValue == ""SSO troubleshooting request"") {

        let strDescription = """"""
        *Customer Name:*  

        *Issuer/Entity ID:*  

        *Org ID* (if applicable):  

        *Group ID* (if applicable): 

        *Eligibility Type* (Example: Eligibility File or RTE or Hybrid):

        *Please provide detailed issue description* (It is helpful to troubleshoot faster):  

        *Issue Member information* (Please provide *First name, Last name, Date of Birth*, and *Health Plan ID*):

        *NOTE: Please get SAML SSO response log from the client. If you get SAML SSO response logs from clients and attach in this Jira ticket, it is faster to troubleshoot.*
        """"""

        description.setValue(strDescription)
    } else if (changedFieldValue == ""SSO certificate (x509 certificate) renewal request"") {

        let strDescription = """"""
        *Customer Name:*

        *Issuer/Entity ID:*

        *Expiration Date of Existing Cert:*

        *Attach x509 certificate file* (.txt file):

        *Contact Information - Please provide the best point of contact for the following:*
        * Any issues with the X509 cert(s)

        *Additional Details:*

        *Note:* Certificate renewals generally do not require a coordinated call as the Teladoc system can handle multiple certs. However, if the customer prefers to arrange a time for cert update, please indicate that in additional details.
        """"""

        description.setValue(strDescription)
    } else if (changedFieldValue == ""Adding test member in UAT environment request"") {

        let strDescription = """"""
        *Customer Name:*  

        *Issuer/Entity ID:*  

        *Org ID (if applicable):*  

        *Group ID (if applicable):*  

        *Eligibility Type* (Example: Eligibility File or RTE or Hybrid):  

        *Test Member information* (Please provide *First name, Last name, Date of Birth*, and *Health Plan ID*):

        *Expected Due Date:*  

        *Additional Details:*  
        """"""

        description.setValue(strDescription)
    }

}",
32,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * PMA project
 * Update ""Request Type"" available options based on ""Issue Type""
 */
console.log(""Behaviours PMA Project – Issue Type started."");

let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();


// This listens if the ""Issue Type"" field has changed
let issueType = getFieldById(getFieldChanged());

let requestType = getFieldById(""Request Type"");

let customField = customFieldManager.getCustomFieldObject(requestType.getFieldId())
let config = customField.getRelevantConfig(getIssueContext())
let options = optionsManager.getOptions(config)
console.log(""issueContext.issueType.name:""+ issueContext.issueType.name)

switch(issueContext.issueType.name){
    case ""Service Request with Approvals"":
        //requestType.setOptionsVisibility(options.filter(it.getValue() in ['New Claims Suppression', 'Extension of an Existing Claims Suppression','Pre Existing Suppression (GM Consults Included)','Price Overrides']})
        requestType.setOptionsVisibility(options.filter(it.getValue() in ['Price Overrides']})
        requestType.setRequired(true);
        requestType.setValue('Price Overrides')
    break;
    case ""Task"":
        requestType.setOptionsVisibility(options.filter(it.getValue() in ['Core','CCM', 'Both']})
        requestType.setRequired(true);
    break;
    default:
        requestType.setRequired(false);
    break;
}",
33,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * PMA project
 * Update ""Request Type"" available options based on ""Issue Type""
 */
console.log(""Behaviours PMA Project – Request Type started."");


let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

let requestType = getFieldById(getFieldChanged());

// This gets the value of ""Request Type"" as String
let requestTypeOption = requestType.getValue() as String

// Custom fields for all Request Type options except for ""Price Overrides"" option
let originalDueDate = getFieldById(""Original due date"");
let dateOccurred = getFieldById(""Date Occurred"");
let endDate = getFieldById(""End Date"");
let dueDate = getFieldById(""Due Date""); // added JIRA-10090
let typeSL = getFieldById(""Type (SL)"");
let isThisaGMConsultsIncludedRequest = getFieldById(""Is this a GM Consults included request?"");
let clientName = getFieldById(""Client Name"");
let doGroupNeedToBeSupressed = getFieldById(""Do group(s) need to be supressed?"");
let areThereAnyProductsThatNeedToBeSuppressed = getFieldById(""Are there any products that need to be suppressed?"");
let productName = getFieldById(""Product Name"");

//Custom fields for ""Price Overrides"" Request Type option
//Description 
let actualEndDate = getFieldById(""Actual End Date"");
let groupPayerCount = getFieldById(""Group/Payer Count"");
let description = getFieldById(""Description"");

let strDescription = """"""
|*Question*|*Answer*|*Question*|*Answer*|
|*Health Plan:*| |*Legacy ID/Group ID:*
{color:#de350b}**If multiple please attach spreadsheet*{color}| |
|*TEIT Ticket #:*
{color:#de350b}**Analysis required before submitting*{color}| |*New, Modification, Removal:*| |
|*Effective Date:*| |*Expiration Date:*| |
|*Pricing (Dollar Amount):*
{color:#de350b}**If multiple please attach spreadsheet*{color}| |*Products:* 
{color:#de350b}**(GM, MH, Derm, VPC, Nutrition)*{color}| |
"""""" 

description.setRequired(true);
productName.setVisible(false);
dateOccurred.setHelpText(""Effective Date of Suppression."");

switch(requestTypeOption){
    case ""New Claims Suppression"":
        actualEndDate.setVisible(false);
        groupPayerCount.setVisible(false);
        //Only run on the create screen
        if ((getActionName() == ""Create"")) {
            description.setValue("""");
        }
        originalDueDate.setVisible(false);
        originalDueDate.setRequired(false);
        dateOccurred.setVisible(true);
        dateOccurred.setRequired(true);
        endDate.setVisible(true);
        endDate.setRequired(true);
        dueDate.setVisible(false); // added JIRA-10090
        
        typeSL.setVisible(true);
        typeSL.setRequired(true);
        //isThisaGMConsultsIncludedRequest.setVisible(true);
        //isThisaGMConsultsIncludedRequest.setRequired(true);
        clientName.setVisible(true);
        clientName.setRequired(true);
        doGroupNeedToBeSupressed.setVisible(true);
        //doGroupNeedToBeSupressed.setRequired(true);
        areThereAnyProductsThatNeedToBeSuppressed.setVisible(true);
        //areThereAnyProductsThatNeedToBeSuppressed.setRequired(true)
    break;
    case ""Extension of an Existing Claims Suppression"":
        actualEndDate.setVisible(false);
        groupPayerCount.setVisible(false);
        //Only run on the create screen
        if ((getActionName() == ""Create"")) {
            description.setValue("""");
        }
        originalDueDate.setVisible(true);
        originalDueDate.setRequired(true);
        dateOccurred.setVisible(false);
        dateOccurred.setRequired(false);
        endDate.setVisible(false);
        endDate.setRequired(false);
        dueDate.setVisible(false); // added JIRA-10090

        typeSL.setVisible(true);
        typeSL.setRequired(true);
        //isThisaGMConsultsIncludedRequest.setVisible(true);
        clientName.setVisible(true);
        clientName.setRequired(true);
        doGroupNeedToBeSupressed.setVisible(true);
        areThereAnyProductsThatNeedToBeSuppressed.setVisible(true);
    break;
    case ""Pre Existing Suppression (GM Consults Included)"":
        actualEndDate.setVisible(false);
        groupPayerCount.setVisible(false);
        //Only run on the create screen
        if ((getActionName() == ""Create"")) {
            description.setValue("""");
        }
        originalDueDate.setVisible(false);
        originalDueDate.setRequired(false);
        dateOccurred.setVisible(true);
        dateOccurred.setRequired(true);
        endDate.setVisible(true)
        endDate.setRequired(true);
        dueDate.setVisible(false); // added JIRA-10090

        typeSL.setVisible(true);
        typeSL.setRequired(true);
        //isThisaGMConsultsIncludedRequest.setVisible(true);
        clientName.setVisible(true);
        clientName.setRequired(true);
        doGroupNeedToBeSupressed.setVisible(true);
        areThereAnyProductsThatNeedToBeSuppressed.setVisible(true);
    break;
    case ""Price Overrides"":
        actualEndDate.setVisible(true);
        groupPayerCount.setVisible(true);
        //Only run on the create screen
        if ((getActionName() == ""Create"")) {
            description.setValue(strDescription);
        }
        originalDueDate.setVisible(false);
        originalDueDate.setRequired(false);
        dateOccurred.setVisible(false);
        dateOccurred.setRequired(false);
        endDate.setVisible(false);
        endDate.setRequired(false);
        dueDate.setVisible(true); // added JIRA-10090

        typeSL.setVisible(false);
        typeSL.setRequired(false);
        //isThisaGMConsultsIncludedRequest.setVisible(false);
        clientName.setVisible(false);
        clientName.setRequired(false);
        doGroupNeedToBeSupressed.setVisible(false);
        areThereAnyProductsThatNeedToBeSuppressed.setVisible(false);
    break;
    default:
        actualEndDate.setVisible(true);
        groupPayerCount.setVisible(true);
        //Only run on the create screen
        if ((getActionName() == ""Create"")) {
            description.setValue("""");
        }
        originalDueDate.setVisible(false);
        dateOccurred.setVisible(false);
        endDate.setVisible(true)

        typeSL.setVisible(false);
        isThisaGMConsultsIncludedRequest.setVisible(false);
        clientName.setVisible(false);
        clientName.setRequired(false);
        doGroupNeedToBeSupressed.setVisible(false);
        areThereAnyProductsThatNeedToBeSuppressed.setVisible(false);
    break;
}",
34,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * PMA project
 * Show - Hide fields based on ""Type (SL)"" custom field
 */
console.log(""Behaviours PMA Project – Type (SL) started."");


let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

let typeSL = getFieldById(getFieldChanged());

// This gets the value of ""Type (SL)"" as String
let typeSLOption = typeSL.getValue() as String

let isThisaGMConsultsIncludedRequest = getFieldById(""Is this a GM Consults included request?"");

switch (typeSLOption){
    case ""Chronic Care Management (CCM)"":
        isThisaGMConsultsIncludedRequest.setVisible(false);
    break;
    case ""Telemedicine"":
        isThisaGMConsultsIncludedRequest.setVisible(true);
    break;
    case ""Both Telemedicine & Chronic Care Management (CCM)"":
        isThisaGMConsultsIncludedRequest.setVisible(true);
    break;
    default:
        isThisaGMConsultsIncludedRequest.setVisible(false);
    break;
}",
35,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor;
// com.atlassian.jira.issue.customfields.option.Option;
/**
 * Eduardo Rojo
 * PMA project
 * Show - Hide fields based on ""Are there any products that need to be suppressed?"" custom field
 */
console.log(""Behaviours PMA Project – Do group(s) need to be supressed? started."");


let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

let areThereAnyProductsThatNeedToBeSuppressed = getFieldById(getFieldChanged());

// This gets the value of ""Do group(s) need to be supressed?"" as String
let areThereAnyProductsThatNeedToBeSuppressedOption = areThereAnyProductsThatNeedToBeSuppressed.getValue() as String

let productName = getFieldById(""Product Name"");

if (areThereAnyProductsThatNeedToBeSuppressedOption == ""Yes""){
    productName.setVisible(true);
    productName.setRequired(true);
}
else{
    productName.setVisible(false);
    productName.setRequired(false);
}",
36,"// Had Imports
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// com.atlassian.jira.component.ComponentAccessor
// org.apache.log4j.Logger
//Creator: Jeff Tompkins
//Purpose: Show other fields dependent on this one
//Change log:

//imports
let customFieldManager = ComponentAccessor.getCustomFieldManager()
let attachmentManager = ComponentAccessor.getAttachmentManager()

@BaseScript FieldBehaviours fieldBehaviours

log = Logger.getLogger(""Behaviour: HHS Release Field Logic"")

let projectName = issueContext?.projectObject?.name
let changedFieldValue = getFieldById(getFieldChanged())?.getValue()

if( changedFieldValue == ""Yes""){
	getFieldById(""Release Notes Title"")?.setRequired(true)
	getFieldById(""Release Notes"")?.setRequired(true)
	getFieldById(""Customer Engagement"")?.setRequired(true)
	getFieldById(""Release Method"")?.setRequired(true)
	getFieldById(""Feature Toggle Name"")?.setRequired(true)
	getFieldById(""Release Images Included"")?.setRequired(true)
	getFieldById(""Region"")?.setRequired(true)


}else{
	getFieldById(""Release Notes Title"")?.setRequired(false)
	getFieldById(""Release Notes"")?.setRequired(false)
	getFieldById(""Customer Engagement"")?.setRequired(false)
	getFieldById(""Release Method"")?.setRequired(false)
	getFieldById(""Feature Toggle Name"")?.setRequired(false)
	getFieldById(""Release Images Included"")?.setRequired(false)
	getFieldById(""Region"")?.setRequired(false)
}

log.debug(""${projectName}-'Behaviour: HHS Release Field Logic hascompleted'"")",
37,"// Had Imports
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
//Creator: Jeff Tompkins
//Purpose: Show other fields dependent on this one

//imports

@BaseScript FieldBehaviours fieldBehaviours

let changedFieldValue = getFieldById(getFieldChanged())?.getValue()

if( changedFieldValue == ""Product"" || changedFieldValue == ""Superadmin practice configuration"" || changedFieldValue == ""Integration Setting""){
	getFieldById(""Release Method Rationale"")?.setVisible(true)
    getFieldById(""Release Method Rationale"")?.setRequired(true)
}else{
	getFieldById(""Release Method Rationale"")?.setVisible(false)
    getFieldById(""Release Method Rationale"")?.setRequired(false)
}",
38,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.customfields.option.Option
// com.onresolve.jira.groovy.user.FieldBehaviours
// groovy.transform.BaseScript
// com.atlassian.jira.issue.MutableIssue
/**
 * Author: Jeff Tompkins
 * Purpose: Sets the RUN allocation fields based on Project or Project Category if the parent Epic is ""Supporting""
 * Log changes: 
*/


@BaseScript FieldBehaviours fieldBehaviours
let projectName = issueContext.projectObject.name

log.debug(""Behaviours: ${projectName} - Auto-populate RUN Fields Initialiser started."")

// Managers
let customFieldManager = ComponentAccessor.getCustomFieldManager();
let optionsManager = ComponentAccessor.getOptionsManager();

// Get the Field Names
let engDepartment = getFieldById(""Eng - Department"");
let engPlatform = getFieldById(""Eng - Platform"");
let deployedLocation = getFieldById(""Deployed Location"");
let deployedProduct = getFieldById(""Deployed Product"");
let runType = getFieldById(""RUN Type"");
let featureToggle = getFieldById(""Feature Toggle"");

// Main Script Body

// If this is a new issue being created, or an existing issue being edited
if(getAction().toString() == ""Create"" || getActionName() == null) {

	// Monitor the Epic Link field for any changes
	let changedFieldValue = getFieldById(getFieldChanged())?.getValue()
	let epicLinkKey = changedFieldValue.toString().substring(4)

	// If the Epic Link field is set
	if(changedFieldValue != null){
    
    	// Get the value of the ""Build/Run"" custom field for the linked Epic
		let issueManager = ComponentAccessor.getIssueManager()
		let parentIssue = issueManager.getIssueObject(epicLinkKey)
    	let buildRunField = customFieldManager.getCustomFieldObjectsByName(""Build/Run"")
		let parentMutableIssue = (MutableIssue) parentIssue // cast parentIssue to MutableIssue interface
    	let buildRunValue = parentMutableIssue.getCustomFieldValue(buildRunField[0])

		// If the Epic's ""Build/Run"" field is ""Supporting""
		if (buildRunValue.toString() == ""Supporting"") {

            // If the Project belongs to the Marketing team:  DEPG, DSPG, DSPGCCM (JIRA-9669)
            if ((issueContext?.getProjectObject().getName().includes(""Data Science Product Growth"")) || (issueContext?.getProjectObject().getName().includes(""Data Engineering Product Growth""))) {
                // If the ""Eng - Department"" field is not set
                if (!engDepartment.getValue()) {
                    engDepartment.setValue(""Data & AI"")
                }
                // If the ""Eng - Platform"" field is not set
                if (!engPlatform.getValue()) {
                    engPlatform.setValue(""Data & AI"")
                }
                // If the ""Run Type"" field is not set
                if (!runType.getValue()) {
                    runType.setValue(""Performance and Operational Improvements"")
                }
                // If the ""Deployed Product"" field is not set
                if (!deployedProduct.getValue()) {
                    if (issueContext?.getProjectObject().getName().includes(""Data Science Product Growth CCM"")) {
                        deployedProduct.setValue(""CCM"")
                    } else {
                        deployedProduct.setValue(""Global Telemed"")
                    }
                }
                // If the ""Deployed Location"" field is not set
                if (!deployedLocation.getValue()) {
                    deployedLocation.setValue(""US"")
                }
                // If the ""Feature Toggle"" field is not set
                if (!featureToggle.getValue()) {
                    featureToggle.setValue(""NO"")
                }
            }

            // If the Project belongs to the UCS team:  UCSA, UCSI, UCSS, UCSW (JIRA-9674)
            if (issueContext?.getProjectObject().getName().includes(""UCS"")) {
                // If the ""Eng - Department"" field is not set
                if (!engDepartment.getValue()) {
                    engDepartment.setValue(""Clinical"")
                }
                // If the ""Eng - Platform"" field is not set
                if (!engPlatform.getValue()) {
                    engPlatform.setValue(""Clinical"")
                }
                // If the ""Run Type"" field is not set
                if (!runType.getValue()) {
                    runType.setValue(""Performance and Operational Improvements"")
                }
                // If the ""Deployed Product"" field is not set
                if (!deployedProduct.getValue()) {
                    deployedProduct.setValue(""HHS"")
                }
                // If the ""Deployed Location"" field is not set
                if (!deployedLocation.getValue()) {
                    deployedLocation.setValue(""US"")
                }
            }

            // If the Project is MEX  (JIRA-9749)
            if (issueContext?.getProjectObject().getKey().includes(""MEX"")) {
                // If the ""Eng - Department"" field is not set
                if (!engDepartment.getValue()) {
                    engDepartment.setValue(""Consumer"")
                }
                // If the ""Eng - Platform"" field is not set
                if (!engPlatform.getValue()) {
                    engPlatform.setValue(""Consumer"")
                }
                // If the ""Run Type"" field is not set
                if (!runType.getValue()) {
                    runType.setValue(""Production Support"")
                }
                // If the ""Deployed Product"" field is not set
                if (!deployedProduct.getValue()) {
                    deployedProduct.setValue(""Global Telemed"")
                }
                // If the ""Deployed Location"" field is not set
                if (!deployedLocation.getValue()) {
                    deployedLocation.setValue(""US"")
                }
            }

        }

    }

}

console.log(""Behaviours: ${projectName} - Auto-populate RUN Fields Initialiser completed."")",
39,"// Had Imports
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.issue.MutableIssue
// org.apache.log4j.Logger
// org.apache.log4j.Level
/*
Creator: Jeff Tompkins
Purpose: If an issue's parent Epic has the ""Build/Run"" field set to ""Supporting"", then make the 5 Run Allocation fields visible and required
The 2 ""Eng"" fields, ""Eng - Platform"" and ""Eng - Department"" will ALWAYS be visible for Stories and Bugs, but will be REQUIRED for RUN Epics.
*/

// import com.atlassian.jira.issue.fields.CustomField
// import com.atlassian.jira.issue.Issue

let log = Logger.getLogger(""Behaviour: Run Allocation Field Logic"")
log.setLevel(Level.INFO)

let projectName = issueContext.projectObject.name
log.debug(""Behaviour Script '${projectName} - Hide Run Allocation Fields' has started."")

// If this is a new issue being created
if(getAction().toString() == ""Create"") {

	// Monitor the Epic Link field for any changes
	let changedFieldValue = getFieldById(getFieldChanged())?.getValue()
	let epicLinkKey = changedFieldValue.toString().substring(4)

	// If the Epic Link field is set
	if( changedFieldValue != null){

		// Get the value of the ""Build/Run"" custom field for the linked Epic
		let issueManager = ComponentAccessor.getIssueManager()
		let customFieldManager = ComponentAccessor.getCustomFieldManager()
		let parentIssue = issueManager.getIssueObject(epicLinkKey)
    	let buildRunField = customFieldManager.getCustomFieldObjectsByName(""Build/Run"")
		let parentMutableIssue = (MutableIssue) parentIssue // cast parentIssue to MutableIssue interface
    	let buildRunValue = parentMutableIssue.getCustomFieldValue(buildRunField[0])

		// If the Epic's ""Build/Run"" field is ""Supporting""
		if (buildRunValue.toString() == ""Supporting"") {

			// Show the Run Allocation fields and set them as required
			getFieldById(""Eng - Department"").setVisible(true).setRequired(true)
			getFieldById(""Eng - Platform"").setVisible(true).setRequired(true)
			getFieldById(""RUN Type"").setVisible(true).setRequired(true)
			getFieldById(""Deployed Location"").setVisible(true).setRequired(true)
			getFieldById(""Deployed Product"").setVisible(true).setRequired(true)

		// If the Epic's ""Build/Run"" field is not ""Supporting""
		} else {

			// Hide the Run Allocation fields, clear their values, and make them optional
			getFieldById(""Eng - Department"").setVisible(true).setRequired(false).setValue(null)
			getFieldById(""Eng - Platform"").setVisible(true).setRequired(false).setValue(null)
			getFieldById(""RUN Type"").setVisible(false).setRequired(false).setValue(null)
			getFieldById(""Deployed Location"").setVisible(false).setRequired(false).setValue(null)
			getFieldById(""Deployed Product"").setVisible(false).setRequired(false).setValue(null)
		}

	// If the Epic Link field is not set
	} else {

		// Hide the Run Allocation fields, clear their values, and make them optional
		getFieldById(""Eng - Department"").setVisible(true).setRequired(false).setValue(null)
		getFieldById(""Eng - Platform"").setVisible(true).setRequired(false).setValue(null)
		getFieldById(""RUN Type"").setVisible(false).setRequired(false).setValue(null)
		getFieldById(""Deployed Location"").setVisible(false).setRequired(false).setValue(null)
		getFieldById(""Deployed Product"").setVisible(false).setRequired(false).setValue(null)
	}

// If this is an existing issue being edited
} else {

	// Hide the Run Allocation fields and make them optional
	getFieldById(""Eng - Department"").setVisible(true).setRequired(false)
	getFieldById(""Eng - Platform"").setVisible(true).setRequired(false)
	getFieldById(""RUN Type"").setVisible(false).setRequired(false)
	getFieldById(""Deployed Location"").setVisible(false).setRequired(false)
	getFieldById(""Deployed Product"").setVisible(false).setRequired(false)

	// Monitor the Epic Link field for any changes
	let changedFieldValue = getFieldById(getFieldChanged()).getValue()
	let epicLinkKey = changedFieldValue.toString().substring(4)

	// If the Epic Link field is set
	if( changedFieldValue != null){

		// Get the value of the ""Build/Run"" custom field for the linked Epic
		let issueManager = ComponentAccessor.getIssueManager()
		let customFieldManager = ComponentAccessor.getCustomFieldManager()
		let parentIssue = issueManager.getIssueObject(epicLinkKey)
    	let buildRunField = customFieldManager.getCustomFieldObjectsByName(""Build/Run"")
		let parentMutableIssue = (MutableIssue) parentIssue // cast parentIssue to MutableIssue interface
    	let buildRunValue = parentMutableIssue.getCustomFieldValue(buildRunField[0])

		// Set the visibility of target fields based on the value of the ""Build/Run"" field
		if (buildRunValue.toString() == ""Supporting"") {

			// Show the Run Allocation fields and set them as required
			getFieldById(""Eng - Department"").setVisible(true).setRequired(true)
			getFieldById(""Eng - Platform"").setVisible(true).setRequired(true)
			getFieldById(""RUN Type"").setVisible(true).setRequired(true)
			getFieldById(""Deployed Location"").setVisible(true).setRequired(true)
			getFieldById(""Deployed Product"").setVisible(true).setRequired(true)
		}

	// If the Epic Link field is not set
	} else {

		// Hide the Run Allocation fields, clear their values, and make them optional
		getFieldById(""Eng - Department"").setVisible(true).setRequired(false).setValue(null)
		getFieldById(""Eng - Platform"").setVisible(true).setRequired(false).setValue(null)
		getFieldById(""RUN Type"").setVisible(false).setRequired(false).setValue(null)
		getFieldById(""Deployed Location"").setVisible(false).setRequired(false).setValue(null)
		getFieldById(""Deployed Product"").setVisible(false).setRequired(false).setValue(null)
	}
}

log.debug(""Behaviour Script '${projectName} - Hide Run Allocation Fields' has completed."")",
40,,
41,"// Had Imports
// com.atlassian.jira.project.Project
// com.atlassian.jira.issue.Issue
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.user.ApplicationUser
// com.atlassian.jira.security.roles.ProjectRole
// com.atlassian.jira.security.roles.ProjectRoleManager
// org.apache.log4j.Logger
// org.apache.log4j.Level
/*
Author: Jeff Melies
Purpose: Restrict the fields Sprint & Priority to only members in the project roles ""Team Member"" or ""Administrators""
request: JIRA-9123  (updated in JIRA-9621 by Jeff T)
*/
//import com.atlassian.jira.security.roles.ProjectRoleActors
log.setLevel(Level.INFO)
Logger log = Logger.getLogger(""Behaviour_${issueContext?.projectObject?.name}_Priority"")

Issue issue = underlyingIssue
let projectRoleManager = ComponentAccessor?.getComponent (ProjectRoleManager)

ApplicationUser loggedInUser = ComponentAccessor?.jiraAuthenticationContext?.loggedInUser
ProjectRole projRole = projectRoleManager?.getProjectRole(""Team Member"")
ProjectRole projRole2 = projectRoleManager?.getProjectRole(""Administrators"")

let projectObj = issue?.getProjectObject() as Project
let priority = getFieldById(""Priority"")

if(issueContext?.projectObject){
    //If loggedInUser is in projRole then allow them to change the fields
    if(loggedInUser?.isMemberOfRole(projRole, issueContext?.projectObject) || loggedInUser?.isMemberOfRole(projRole2, issueContext?.projectObject)){
        getFieldById(""Priority"")?.setReadOnly(false)
    }
}else{
    getFieldById(""Priority"")?.setReadOnly(true)
}","let context = await getContext()
let projectKey = context.extension.project.key
let resMyRolesInProject = await makeRequest(`/rest/api/2/project/${projectKey}/roledetails?currentMember=True`)
let myRolesInProject = resMyRolesInProject.body    

let inProjectRole = (roleName) => {
    return myRolesInProject.find(r => r.name == roleName) != null
}

let isTeamMember = inProjectRole(""Team Member"")
let isAdministrator = inProjectRole(""Administrators"")

if(isTeamMember || isAdministrator) {
    getFieldById(""customfield_10090"").setReadOnly(false) // Field (Priority)
} else {
    getFieldById(""customfield_10090"").setReadOnly(true) // Field (Priority)
}

"
42,"// Had Imports
// com.atlassian.jira.project.Project
// com.atlassian.jira.issue.Issue
// com.atlassian.jira.component.ComponentAccessor
// com.atlassian.jira.user.ApplicationUser
// com.atlassian.jira.security.roles.ProjectRole
// com.atlassian.jira.security.roles.ProjectRoleManager
// org.apache.log4j.Logger
// org.apache.log4j.Level
/*
Author: Jeff Melies
Purpose: Restrict the fields Sprint & Priority to only members in the project roles ""Team Member"" or ""Administrators""
request: JIRA-9123  (updated in JIRA-9621 by Jeff T)
*/
//import com.atlassian.jira.security.roles.ProjectRoleActors
log.setLevel(Level.INFO)
Logger log = Logger.getLogger(""Behaviour_${issueContext?.projectObject?.name}_Sprint"")

let projectRoleManager = ComponentAccessor?.getComponent (ProjectRoleManager)

Issue issue = underlyingIssue 
ApplicationUser loggedInUser = ComponentAccessor?.jiraAuthenticationContext?.loggedInUser
ProjectRole projRole = projectRoleManager?.getProjectRole(""Team Member"")
ProjectRole projRole2 = projectRoleManager?.getProjectRole(""Administrators"")

let projectObj = issue?.getProjectObject() as Project

if(issueContext?.projectObject){
    //If loggedInUser is in projRole then allow them to change the fields
    if(loggedInUser?.isMemberOfRole(projRole, issueContext?.projectObject) || loggedInUser?.isMemberOfRole(projRole2, issueContext?.projectObject)){
        getFieldById(""Sprint"").setReadOnly(false)
    }
}else{
    getFieldById(""Sprint"").setReadOnly(true)
}","let context = await getContext()
let projectKey = context.extension.project.key
let resMyRolesInProject = await makeRequest(`/rest/api/2/project/${projectKey}/roledetails?currentMember=True`)
let myRolesInProject = resMyRolesInProject.body    

let inProjectRole = (roleName) => {
    return myRolesInProject.find(r => r.name == roleName) != null
}

let isTeamMember = inProjectRole(""Team Member"")
let isAdministrator = inProjectRole(""Administrators"")

if(isTeamMember || isAdministrator) {
    getFieldById(""customfield_10090"").setReadOnly(false) // Field (Sprint)
} else {
    getFieldById(""customfield_10090"").setReadOnly(true) // Field (Sprint)
}
"