Behaviors are quite hard to rewrite on jira cloud, When a behavior is using getAction() for a transition, if the transition is different than 1 (1 means create) - you can ignore the script, only add a note to review.

You don't use any sort of import
You cannot make anything "dynamic" everything must be hardcoded.
Some examples in the behaviors.csv file

All fields must be get by ID
the function to do it is getFieldById("")

I'll need to replace the customfield IDs after the production migration, so do the following -> 

when adding the id inside the function, add any customfield id, such as customfield_10090, but you need to add a comment in the right of the line, as " // Field (<field name here>)"
This way I can treat it after the migration. I can replace the customfield IDS using a code I have.

Some examples below

```typescript
// Access the context for the UI Modification
const context = await getContext()

// Get the current issue key
const issueKey = context.extension.issue.key;

// Get the current issue object 
const issue = await makeRequest("/rest/api/2/issue/" + issueKey);

// Log out to the browser some field values to show how to get them from the issue.  
// Note you can access any field on an issue through the fields property

console.log("Issue Field Values:")
console.log("Status field: ", issue.body.fields.status)
console.log("Assignee field: ", issue.body.fields.assignee)
```

```typescript
//Auto assign issue based on priority
const priorityField = getFieldById("priority")
const assigneeField = getFieldById("assignee")

console.log(priorityField.getValue().name)

switch (priorityField.getValue().name) {
    case "Highest":
        assigneeField.setValue("USER1")
        break;
    case "Medium":
        assigneeField.setValue("USER2")
        break;
    default:
        break;
}
```

```typescript
const context = await getContext()
const fieldsResp = await makeRequest("/rest/api/2/field");

// Project & Issuetype
const issueType = context.extension.issueType.id
const projectKey = context.extension.project.key

async function getIssue(ikey) {
	let res = await makeRequest("/rest/api/2/issue/"+ikey);
	return res.body
}	
```

# This is a very common script (Default Description)
```typescript
let desc = getFieldById("description")

let defaultValue = `any text here`.replaceAll(/    /, '')

if (!desc.getValue().toString()) { 
    desc.setValue(defaultValue)
}
```