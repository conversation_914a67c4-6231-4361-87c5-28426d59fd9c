# How to rewrite listeners from <PERSON>riptr<PERSON>ner Server to Cloud?

1. Get the data from each row in the "Script From" understand what that script does, rewrite it using the examples below. If this require any review, add another column named "review" with the value True to it.

Listeners will need to be rewritten using unirest. 

Listeners are triggered by pre-programmed events, using that event's data you can interact with it's object in context. Below you can see the data.

Events and parameter examples below

|               Event              |                                                                                                                                                         Binding/Context                                                                                                                                                         |                                                                          Parameter Examples                                                                          |   |   |
|:--------------------------------:|:-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------:|:--------------------------------------------------------------------------------------------------------------------------------------------------------------------:|---|---|
| Attachment Created               | attachment The attachment details as a Map. See Get Attachment REST API reference for details.                                                                                                                                                                                                                                  | attachment.filename, attachment.author.displayName, attachment.content                                                                                               |   |   |
| Attachment Deleted               |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Board Created                    | board The board details as a Map. See Get Board REST API reference for details.                                                                                                                                                                                                                                                 | board.id, board.name, board.type                                                                                                                                     |   |   |
| Board Deleted                    |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Board Updated                    |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Board Configuration Changed      |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Comment Created                  | comment The comment details as a Map. See Get Comment REST API reference for details. issue Limited issue details as a Map. It has id, self, key and fields(status, priority, assignee, project, issuetype, summary) properties.                                                                                                | comment.body, comment.author.displayName, issue.key                                                                                                                  |   |   |
| Comment Updated                  |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Comment Deleted                  |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Issue Created                    | issue The issue details as a Map. See Get Issue REST API reference for details. user The user details as a Map. See Get User REST API reference for details. issue_event_type_name A String containing: issue_created For Issue Updated Only changelogThe changelog details as a Map. See Webhook Changelog Example for details | issue.key, user.displayName, issue.fields.project.key                                                                                                                |   |   |
| Issue Updated                    |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Issue Deleted                    |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Issue Link Created               | issueLink The issue link details as a Map, available fields: id, sourceIssueId, destinationIssueId, issueLinkType. See Get Issue Link Type REST API reference for details.                                                                                                                                                      |                                                                                                                                                                      |   |   |
| Issue Link Deleted               |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Issue Type Created               | issueType The issue type details as a Map. See Issue Type REST API reference for details.                                                                                                                                                                                                                                       | project.id,  project.key,  issuetype.id,                                                                                                                             |   |   |
| Issue Type Updated               |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Issue Type Deleted               |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Option Attachments Changed       | property The Jira configuration as a Map. Available fields: self, key, value.                                                                                                                                                                                                                                                   | property.key,  property.value                                                                                                                                        |   |   |
| Option Issuelinks Changed        |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Option Subtasks Changed          |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Option Timetracking Changed      |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Option Unassigned Issues Changed |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Option Voting Changed            |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Option Watching Changed          |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Project Created                  | project The project details as a Map. See Get Project REST API reference for details.                                                                                                                                                                                                                                           | project.key,  project.lead.displayName                                                                                                                               |   |   |
| Project Deleted                  |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Project Updated                  |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Sprint Created                   | sprint The sprint details as a Map. See Get Sprint REST API reference for details.                                                                                                                                                                                                                                              | sprint.name,  sprint.id                                                                                                                                              |   |   |
| Sprint Started                   |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Sprint Closed                    |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Sprint Deleted                   |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Sprint Updated                   |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| User Created                     | user The user details as a Map. See Get User REST API reference for details.                                                                                                                                                                                                                                                    | user.displayName,  user.accountId                                                                                                                                    |   |   |
| User Updated                     |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| User Deleted                     |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Version Created                  | version The Project Version details as a Map. See Get Version REST API reference for details.                                                                                                                                                                                                                                   | version.name,  version.self,  version.id,  version.description,  version.archived,  version.released,  version.overdue,  version.userReleaseDate,  version.projectId |   |   |
| Version Updated                  |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Version Deleted                  |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Version Moved                    |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Version Released                 |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Version Unreleased               |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Worklog Created                  | worklog The Worklog details as a Map. See Get Worklog REST API reference for details.                                                                                                                                                                                                                                           | worklog.id,  worklog.author.displayName, worklog.updateAuthor.displayName                                                                                            |   |   |
| Worklog Updated                  |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |
| Worklog Deleted                  |                                                                                                                                                                                                                                                                                                                                 |                                                                                                                                                                      |   |   |

Once rewriting the listener, add a comment in the beginning explaining what the code does.

Some examples on how to write functions below

```groovy

def search(jql, maxResults = 5000, fields = null, expand = null) {
    logger.info("[GET] Searching with JQL: ${jql}")
    
    def queryParams = [
        'jql': jql,
        'maxResults': maxResults
    ]
    
    // Add optional parameters if provided
    if (fields) {
        queryParams['fields'] = fields
    }
    if (expand) {
        queryParams['expand'] = expand
    }
    
    def resp = get("/rest/api/2/search")
                .header('Accept', 'application/json')
                .queryString(queryParams)
                .asString()
    
    if (resp.status == 200) {
        logger.info("Successfully executed JQL search. Found ${resp.body.total} issues.")
    } else {
        logger.error("Failed to execute JQL search. Response: ${resp.body}")
    }
    
    return resp
}

def set(issuekey, data) {
    logger.info("[PUT] Updating ${issuekey} with ${data.toString()}")
    def resp = put("/rest/api/2/issue/${issuekey}")
                .header('Content-Type', 'application/json')
                .queryString('notifyUsers', false)
                .body([fields: data])
                .asString()
    if (resp.status == 204) {
        logger.info("Successfully updated issue ${issuekey}.")
    } else {
        logger.error("Failed to update issue ${issuekey}. Response: ${resp.body}")
    }
    return resp
}
```

```groovy
def fields = get('/rest/api/2/field')
    .asObject(List)
    .body as List<Map>
    
def issuetypes = get('/rest/api/2/issuetype')
    .asObject(List)
    .body as List<Map>
    
def projects = get('/rest/api/2/project')
    .asObject(List)
    .body as List<Map>
```

```groovy
def getFieldId = { fieldName ->
    return fields.find { it.name == fieldName }?.id
}
```
