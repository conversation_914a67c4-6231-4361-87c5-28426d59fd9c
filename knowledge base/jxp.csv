"id","code"
"require attachments","issue.attachments.some(attachment => attachment.mimeType == 'application/pdf')"
"restrict transition permissions","user.getProjectRoles(project).some(p => p.name == "userrole")"
"require sub tasks","issue.subtasks.length > 0"
"sub tasks must be done","issue.subtasks.every(subtask => subtask.status.name == 'Done')"
"sub tasks must have assignee","issue.subtasks.every(subtask => subtask.assignee != null)"
"subtask type and status","issue     .subtasks     .filter(s => s.issueType.name == 'Scope Change')     .every(d => d.status.name == 'Done')"
"require one linked issue","issue.links.length > 0"
"multi select list or checkbox field must be populated with specific value","issue.customfield_10263 ?     issue.customfield_10263.some(option => option.value == "End Users") :     false"
"multi select or checkbox must be populated with one specific value","issue.customfield_10214 ?     issue.customfield_10214.some(option => option.value == "A") :     false"
"multi select or checkbox field equals a specific set of values","issue.customfield_10214 ?     issue.customfield_10214.map(option => option.value) == ['Yes', 'No'] :     false"
"sub tasks must be in progress","issue.subtasks.every(subtask => subtask.status.name == 'In Progress')"
"minimum length of issue description","issue.description.plainText.length > 30"
"minimum length of all comments","issue.comments.every(comment => comment.body.plainText.length > 10)"
"issue must be in the current active sprint","issue.sprint?.state == 'active'"
"require attachments","issue.attachments.filter(attachment => attachment.id == null).length == 3"
"require at least two pdf attachments are added during the current transition","issue.attachments.filter(attachment => attachment.id == null && attachment.mimeType == 'application/pdf').length > 1"
"checks the issue has been in a status previously","issue.changelogs.some(c =>     c.items.some(i => i.toString == 'In Progress') )"
"fields required","issue.customfield_10040 != null"
"linked issues","// Check all linked issue are in certain status issue.links.every(l => l.linkedIssue.status.name == 'Done')  // Check all linked issues of a certain link type are at certain status issue.links     .filter(l => l.type.inward == 'is blocked by')     .every(l => l.linkedIssue.status.name == 'Done')  // Check all linked issues have a resolution issue.links.every(l => l.linkedIssue.resolution != null)  // Check all linked issues of a certain link type have a resolution issue.links     .filter(l => l.type.inward == 'is blocked by')     .every(l => l.linkedIssue.resolution != null)"
"regular expressions","issue.description.plainText.match("SRJ-\d+") != null"
"user in fields","issue.customfield_10213?.displayName == 'A User'  // or using accountId issue.customfield_10213?.accountId == '5cf7c174eba28b4ea84a7cb5'"
"users and user groups","['User A','User B','User C'].includes(user.displayName)"
"issue must have at least one pdf attachment","issue.attachments.some(attachment => attachment.mimeType == 'application/pdf')"
"issue must have at least three pdf attachments","issue.attachments.filter(attachment => attachment.mimeType == 'application/pdf').length > 3"
"specify the current user must be in a defined list of users","['accountIdHere', 'accountIdHere'].includes(user.accountId)"
"current logged in user has added at least one comment","issue.comments.some(c => c.author.accountId == user.accountId)"
"fields changed","issue.changelogs.some(c => c.items.some(i => i.field == 'Field Name'))"
"last field changed","issue.changelogs[0].items.some(i => i.field == 'Field Name')"
"require at least one fix version","issue.fixVersions.length > 0"
"require at least one component","issue.components.length > 0"
"description field must contain more than 30 characters","issue.description.plainText.length > 30"
"require all issues in an epic must be done","issue.isEpic && issue.stories.every(story => story.status.name == 'Done')"
"require specific users in a project can create issues of a specified type","issue.issueType.name == 'Bug' ?      user.getProjectRoles(project).map(p => p.name).includes("Developers") :      true"
"specify that one of two label fields must have a value","issue.customfield_12345 != null || customfield_67890 != null"
"date comparison","issue.created < new Date().minusDays(7)"
"verify issue type","["Bug", "Task"].includes(issue.issueType.name)"
"require a comment on transition","issue.comments.some(comment => comment.id == null)"
"example of incorrect condition script","issue.comments.map(c => c.body)"
"comments","issue.comments.some(comment => comment.id == null)"
"field is not empty","["Bug", "Task"].includes(issue.issueType.name)"