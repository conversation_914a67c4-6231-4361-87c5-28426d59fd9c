The source file to rewrite jira expressions is in the source/jxp.csv file. Add a column of "Script to" and write your rewritten scripts in each line, corresponding to each row.

Jira Expressions are one of the hardest to rewrite.

To do so successfully keep in mind you will need to write code that will alwasy return a boolean expression without declaration of variables nor functions. This expression must always return true or false and in some cases must leave it open to "everything else" such as if the code needs to run in a scenario or anything else.

- Examples of usable jira expressions are in the file jxp.csv in the knowledge base folder.

Example of jira expression:
`issue.attachments.some(attachment => attachment.mimeType == 'application/pdf')`

Other examples available in the csv file.

If any customfield is refferenced, add a new column in the final csv file for "fields refferenced" and possibly reference by name if available, else you can put the customfield ID and I can check it myself.