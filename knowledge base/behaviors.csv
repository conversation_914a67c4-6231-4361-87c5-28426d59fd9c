Script From,Script Cloud
"import com.atlassian.jira.component.ComponentAccessor
import com.atlassian.jira.security.roles.ProjectRoleManager

import static com.atlassian.jira.issue.IssueFieldConstants.ISSUE_TYPE

def projectRoleManager = ComponentAccessor.getComponent(ProjectRoleManager)
def allIssueTypes = ComponentAccessor.constantsManager.allIssueTypeObjects

def user = ComponentAccessor.jiraAuthenticationContext.loggedInUser
def issueTypeField = getFieldById(ISSUE_TYPE)

def remoteUsersRoles = projectRoleManager.getProjectRoles(user, issueContext.projectObject)*.name
def availableIssueTypes = []

if (""Administrators"" in remoteUsersRoles) {
    availableIssueTypes.addAll(allIssueTypes.findAll { it.name in [""Incident"", ""Problem"", ""Makegood"", ""Post Mortem"",""Sub-Task""] })
}

else {
    availableIssueTypes.addAll(allIssueTypes.findAll { it.name in [""Incident""] })
}

issueTypeField.setFieldOptions(availableIssueTypes)","let context = await getContext()
let projectKey = context.extension.project.key
let myRolesInProject = await makeRequest(""/rest/api/2/project/CE/roledetails?currentMember=True"")

let myRoles = []
myRolesInProject.body.map( role => {
        myRoles.push(role.name)
})

let issueTypes = await makeRequest(`/rest/api/2/project/${projectKey}`)
let optionsDisabled = []

issueTypes.body.issueTypes.map( itype => {
        if (!myRoles.includes(""Administrators"") && itype.name != ""Incident"") {
                optionsDisabled.push(itype.id)
        }
})

getFieldById(""issuetype"").setOptionsVisibility(optionsDisabled, false)"
"// Get the fields you need
def cField = getFieldByName(""Original Committed Date"")

// Turn the value from sosStatus into a string for evaluating
def cFieldValue = cField.getValue()
//log.warn(""--------------------This is to test Behaviours Logging----------------"")


// Evaluate the selected value; in this case, does it contain the string ""Removed""

if(cFieldValue == """")
 {
    cField.setReadOnly(false);
    //log.warn ""cFieldValue is null""
 }

else if (cFieldValue != null)
 {
    //cField.setReadOnly(true);
    //log.warn ""cFieldValue:"" + cField.getValue()
 }","// Get the fields you need
let cField = getFieldById(""customfield_10092"")

// Turn the value from sosStatus into a string for evaluating
let cFieldValue = cField.getValue()
//console.log(""--------------------This is to test Behaviours Logging----------------"")


// Evaluate the selected value; in this case, does it contain the string ""Removed""
if(cFieldValue == """")
 {
    cField.setReadOnly(false);
    //console.log ""cFieldValue is null""
 }
"
"import com.atlassian.jira.component.ComponentAccessor
import com.atlassian.jira.security.roles.ProjectRoleManager
 
import static com.atlassian.jira.issue.IssueFieldConstants.ISSUE_TYPE
 
def projectRoleManager = ComponentAccessor.getComponent(ProjectRoleManager)
def allIssueTypes = ComponentAccessor.constantsManager.allIssueTypeObjects
 
def user = ComponentAccessor.jiraAuthenticationContext.loggedInUser
def issueTypeField = getFieldById(ISSUE_TYPE)
 
def remoteUsersRoles = projectRoleManager.getProjectRoles(user, issueContext.projectObject)*.name
def availableIssueTypes = []
 
if (""Administrators"" in remoteUsersRoles) {
    availableIssueTypes.addAll(allIssueTypes)
}
 
else {
    availableIssueTypes.addAll(allIssueTypes.findAll { it.name in [""Bug"",""Improvement"",""New Feature"",""Problem""] })
}
 
issueTypeField.setFieldOptions(availableIssueTypes)","let context = await getContext()
let projectKey = context.extension.project.key
let myRolesInProject = await makeRequest(""/rest/api/2/project/CE/roledetails?currentMember=True"")

let myRoles = []
myRolesInProject.body.map( role => {
        myRoles.push(role.name)
})

let issueTypes = await makeRequest(`/rest/api/2/project/${projectKey}`)
let optionsDisabled = []

issueTypes.body.issueTypes.map( itype => {
        if (!myRoles.includes(""Administrators"") && itype.name != ""Incident"") {
                optionsDisabled.push(itype.id)
        }
})

getFieldById(""issuetype"").setOptionsVisibility(optionsDisabled, false)"
"def dependencyForTeam = getFieldByName(""Dependency for Team"")
def labels = getFieldByName(""Labels"")
def dueDate = getFieldByName(""Due Date"")

def dependencyString = dependencyForTeam.getValue() as String

def dependencyLabel = ""UNACCEPTED-DEPENDENCY""
def acceptedDependencyLabel = ""ACCEPTED-DEPENDENCY""
def currentLabels = labels.getValue() as String
def indexOfDependencyLabel = currentLabels.indexOf(dependencyLabel)
def prefixLabel = """"


if(dependencyString != ""[null]""){
    dueDate.setRequired(true);
    if (indexOfDependencyLabel == -1 && currentLabels.indexOf(acceptedDependencyLabel) == -1) {
        if (currentLabels.length() != 0) {
            prefixLabel = "", ""
        } else {
            prefixLabel = """"
        }
       	
        if (currentLabels.indexOf(""["") == -1) {
            if (currentLabels.length() != 0 && indexOfDependencyLabel == -1) {
    			currentLabels = currentLabels + prefixLabel + dependencyLabel
            } else {
                currentLabels = dependencyLabel
            }
        } else {
            currentLabels = currentLabels.substring(1,currentLabels.length()-1) + prefixLabel + dependencyLabel
        }
        labels.setFormValue(currentLabels);
    }
 } else {
    dueDate.setRequired(false);
    if (indexOfDependencyLabel != -1) {
        if (currentLabels.indexOf(""["") != -1) {
    		currentLabels = currentLabels.substring(1,indexOfDependencyLabel-2) + currentLabels.substring(indexOfDependencyLabel + dependencyLabel.length(), currentLabels.length()-1)
        } else {
            currentLabels = """"
        }
        labels.setFormValue(currentLabels);
        
    }
 }","
let dependencyForTeam = getFieldById(""customfield_10047"")
let labels = getFieldById(""labels"")
let dueDate = getFieldById(""Due Date"")

let dependencyString = dependencyForTeam.getValue() as String

let dependencyLabel = ""UNACCEPTED-DEPENDENCY""
let acceptedDependencyLabel = ""ACCEPTED-DEPENDENCY""
let currentlabels = labels.getValue()
let indexOfDependencyLabel = currentlabels.indexOf(dependencyLabel)
let prefixLabel = """"

if(dependencyString != ""[null]""){
    dueDate.setRequired(true);
    if (indexOfDependencyLabel == -1 && currentlabels.indexOf(acceptedDependencyLabel) == -1) {
        if (currentlabels.length() != 0) {
            prefixLabel = "", ""
        } else {
            prefixLabel = """"
        }
               
        if (currentlabels.indexOf(""["") == -1) {
            if (currentlabels.length() != 0 && indexOfDependencyLabel == -1) {
                            currentlabels = currentlabels + prefixLabel + dependencyLabel
            } else {
                currentlabels = dependencyLabel
            }
        } else {
            currentlabels = currentlabels.substring(1,currentlabels.length()-1) + prefixLabel + dependencyLabel
        }
        labels.setValue(currentlabels);
    }
 } else {
    dueDate.setRequired(false);
    if (indexOfDependencyLabel != -1) {
        if (currentlabels.indexOf(""["") != -1) {
                    currentlabels = currentlabels.substring(1,indexOfDependencyLabel-2) + currentlabels.substring(indexOfDependencyLabel + dependencyLabel.length(), currentlabels.length()-1)
        } else {
            currentlabels = """"
        }
        labels.setValue(currentlabels);
        
    }
 }"
"import com.atlassian.jira.component.ComponentAccessor
import com.atlassian.jira.security.roles.ProjectRoleManager

import static com.atlassian.jira.issue.IssueFieldConstants.ISSUE_TYPE

def projectRoleManager = ComponentAccessor.getComponent(ProjectRoleManager)
def allIssueTypes = ComponentAccessor.constantsManager.allIssueTypeObjects

def user = ComponentAccessor.jiraAuthenticationContext.loggedInUser
def issueTypeField = getFieldById(ISSUE_TYPE)

def remoteUsersRoles = projectRoleManager.getProjectRoles(user, issueContext.projectObject)*.name
def availableIssueTypes = []

if (""Developers"" in remoteUsersRoles) {
    availableIssueTypes.addAll(allIssueTypes/*.findAll { it.name in [""Incident"", ""Problem""] }*/)
}

else {
    availableIssueTypes.addAll(allIssueTypes.findAll { it.name in [""Request""] })
}

issueTypeField.setFieldOptions(availableIssueTypes)","let context = await getContext();
let projectKey = context.extension.project.key;
let myRolesInProject = await makeRequest(`/rest/api/2/project/${projectKey}/roledetails?currentMember=True`);

let myRoles = [];
myRolesInProject.body.forEach(role => {
    myRoles.push(role.name);
});

console.log(""My Roles in This Project: "", myRoles.toString());
let issueTypes = await makeRequest(`/rest/api/2/project/${projectKey}`);
let optionsDisabled = [];
let optionsEnabled = [];

let isDeveloper = false

// Determine which issue types to disable and enable
issueTypes.body.issueTypes.forEach(itype => {
    if (myRoles.includes(""Developers"") && [""Incident Work"", ""Problem""].includes(itype.name)) {
        isDeveloper = true;
        optionsEnabled.push(itype.id);
    } else if (!myRoles.includes(""Developers"") && [""Request""].includes(itype.name)) {
        optionsEnabled.push(itype.id);
    } else {
        optionsDisabled.push(itype.id);
    }
});

// Apply visibility settings
getFieldById(""issuetype"").setOptionsVisibility(optionsDisabled, false);
getFieldById(""issuetype"").setOptionsVisibility(optionsEnabled, true);

// Set options to avoid showing wrong options
if (isDeveloper) {getFieldById(""issuetype"").setValue(optionsEnabled[0].id)}"
,
,
"//Define Fields
//Fields to be altered
def sprint = getFieldByName(""Sprint"")

//Field on which the altering is based
def flaggedField = getFieldByName(""Escalation Team"")
def selectedOption = flaggedField.getValue() as String

log.debug ""Selected option: $selectedOption"" //logs the selection in the main atlassian-jira log
 
//The values of flaggedField you wish to look for when altering

if ((selectedOption == ""AppOps (APPOPS)"") || (selectedOption == ""AppOps Support (AOS)"")){
 sprint.setHidden(false)
}

else {
    if(sprint.value)
    {
        sprint.setFormValue(null)
    }
    sprint.setHidden(true)
}","//Define Fields
//Fields to be altered
let sprint = getFieldById(""customfield_10020"")

//Field on which the altering is based
let flaggedField = getFieldById(""customfield_10391"")
let selectedOption = flaggedField.getValue().value
 
//The values of flaggedField you wish to look for when altering
if ((selectedOption == ""AppOps (APPOPS)"") || (selectedOption == ""AppOps Support (AOS)"")){
    sprint.setVisible(true)
}

else {
    if(sprint.getValue())
    {
        sprint.setValue(null)
    }
    sprint.setVisible(false)
}"
"//Define Fields
//Fields to be altered
def approved = getFieldByName(""Manual CS Effort Approved"")
def longtermplan = getFieldByName(""Manual CS Effort Long Term Plan"")
 
//Field on which the altering is based
def flaggedField = getFieldByName(""Manual CS Effort"")
def selectedOption = flaggedField.getValue() as String

//The values of flaggedField you wish to look for when altering  

if (selectedOption == ""Yes"") {
 approved.setHidden(false)
 approved.setRequired(true)
 longtermplan.setHidden(false)
 longtermplan.setRequired(true)
}
 
else {
 approved.setHidden(true)
 approved.setRequired(false)
 longtermplan.setHidden(true)
 longtermplan.setRequired(false)
}","//Define Fields
//Fields to be altered
let approved = getFieldById(""customfield_10112"") // Id for Manual CS Effort Approved
let longtermplan = getFieldById(""customfield_10113"") // Id for Manual CS Effort Long Term Plan

//Field on which the altering is based
let flaggedField = getFieldById(""customfield_10111"") // Id for Manual CS Effort
let selectedOption = flaggedField.getValue().value as String

//The values of flaggedField you wish to look for when altering  
if (selectedOption == ""Yes"") {
 approved.setVisible(true)
 approved.setRequired(true)
 longtermplan.setVisible(true)
 longtermplan.setRequired(true)
}
 
else {
 approved.setVisible(false)
 approved.setRequired(false)
 longtermplan.setVisible(false)
 longtermplan.setRequired(false)
}"
"// Client Type Script (Required)
// this portion goes on a field behaviour set up on your checkbox field
def checkBoxes = getFieldByName(""Client Type"") // your checkbox field's name
def customField = getFieldByName(""Holding Group"")
def option = ""Buy-Side"" // the option you want to check

//log.debug (""Checkbox Value: ${checkBoxes.value}"")

if (option in checkBoxes.value) {
        customField.setRequired(true);
        customField.setHidden(false);
} else {
    customField.setRequired(false);
    customField.setHidden(true);
}

// Holding Group Field (Hidden)","/*
const cb-example = getFieldById(""customfield_02133"").getValue();  
>>> [{""id"":""10021"",""value"":""A""},{""id"":""10022"",""value"":""B""}]
*/

let checkBoxes = getFieldById(""Client Type"") // Add Field Id
let customField = getFieldById(""Holding Group"") // Add Field Id 

let option = ""Buy-Side"" // the option you want to check

//log.debug (""Checkbox Value: ${checkBoxes.value}"")
let hasOption = checkBoxes.getValue().filter(opt => opt.value == option).size() > 0

if (hasOption) {
        customField.setRequired(true);
        customField.setVisible(true);
} else {
    customField.setRequired(false);
    customField.setVisible(false);
}"
"// Get the fields you need
def updateType = getFieldByName(""Update Type"")
def pdfTarget = getFieldByName(""QA'd with a PDF target build?"")
def webTarget = getFieldByName(""QA'd with a web target build?"")
def docLibrary = getFieldByName(""Added updated PDFs to docLibrary?"")
 
log.debug (""updateType: ${updateType}"")

def updateTypeSelected = updateType.getValue() as String

if (updateType.getValue() != null && updateTypeSelected.contains(""Help Website""))
     {
        // Set fields to show and required
        pdfTarget.setRequired(true);
        webTarget.setRequired(true);
        docLibrary.setRequired(true);

        pdfTarget.setHidden(false);
        webTarget.setHidden(false);
        docLibrary.setHidden(false);
     }

else
{
    pdfTarget.setRequired(false);
    webTarget.setRequired(false);
    docLibrary.setRequired(false);

    pdfTarget.setHidden(true);
    webTarget.setHidden(true);
    docLibrary.setHidden(true);
}","// Get the fields you need
let updateType = getFieldById(""customfield_10209"") // Id for Update Type
let pdfTarget = getFieldById(""customfield_10210"") // Id for QA'd with a PDF target build?
let webTarget = getFieldById(""customfield_10211"") // Id for QA'd with a web target build?
let docLibrary = getFieldById(""customfield_10212"") // Id for Added updated PDFs to docLibrary?

let updateTypeSelected = updateType.getValue().value as String

if (updateType.getValue() != null && updateTypeSelected.includes(""Help Website""))
     {
        // Set fields to show and required
        pdfTarget.setRequired(true);
        webTarget.setRequired(true);
        docLibrary.setRequired(true);

        pdfTarget.setVisible(true);
        webTarget.setVisible(true);
        docLibrary.setVisible(true);
     }

else
{
    pdfTarget.setRequired(false);
    webTarget.setRequired(false);
    docLibrary.setRequired(false);

    pdfTarget.setVisible(false);
    webTarget.setVisible(false);
    docLibrary.setVisible(false);
}"
"def EscTeam = getFieldByName('Esclation Team')
def EscTeamValue = getFieldByName('Escalation Team').value

if (EscTeamValue = !('Cloud Ops (SYS)','Cloud Reliability Engineering (CRE)','TEOM (TEOM)')) 
{
    getFieldByName('Assignee').setHidden(true)
} ","let EscTeam = getFieldById('customfield_10391') // Id for Escalation Team
let EscTeamValue = EscTeam.getValue().value

if (!['Cloud Ops (SYS)','Cloud Reliability Engineering (CRE)','TEOM (TEOM)'].includes(EscTeamValue)) 
{
    getFieldById('assignee').setVisible(false)
}"
"//Define Fields
//Fields to be altered
def vdd = getFieldByName(""Vendor - Data Delay"")
def vdti = getFieldByName(""Vendor - Data Type Issue"")
 
//Field on which the altering is based
def flaggedField = getFieldByName(""Partner Caused Issue"")
def selectedOption = flaggedField.getValue() as String

//The values of flaggedField you wish to look for when altering  

if (selectedOption == ""Yes"") {
 vdd.setHidden(false)
 vdti.setHidden(false)

}
 
else {
vdd.setHidden(true)
vdti.setHidden(true)
 
}","/*
// Map fields for production
*/

// let vdd = getFieldById(""vdd"")    // Vendor Delay
// let vdti = getFieldById(""vdti"")  // Vendor - Data Type Issue
// let pci = getFieldById(""pci"")    // Partner Caused Issue


//Define Fields
//Fields to be altered
let vdd = getFieldById(""customfield_10396"")
let vdti = getFieldById(""customfield_10395"")
 
//Field on which the altering is based
let flaggedField = getFieldById(""customfield_10394"")
let selectedOption = flaggedField.getValue() as String

//The values of flaggedField you wish to look for when altering  

if (selectedOption == ""Yes"") {
 vdd.setVisible(true)
 vdti.setVisible(true)

}
 
else {
vdd.setVisible(false)
vdti.setVisible(false)
 
}"
,
,
"import com.atlassian.jira.component.ComponentAccessor
import com.atlassian.jira.security.roles.ProjectRoleManager
 
import static com.atlassian.jira.issue.IssueFieldConstants.ISSUE_TYPE
 
def projectRoleManager = ComponentAccessor.getComponent(ProjectRoleManager)
def allIssueTypes = ComponentAccessor.constantsManager.allIssueTypeObjects
 
def user = ComponentAccessor.jiraAuthenticationContext.loggedInUser
def issueTypeField = getFieldById(ISSUE_TYPE)
 
def remoteUsersRoles = projectRoleManager.getProjectRoles(user, issueContext.projectObject)*.name
def availableIssueTypes = []
 
if (""Administrators"" in remoteUsersRoles) {
    availableIssueTypes.addAll(allIssueTypes)
}
 
else {
    availableIssueTypes.addAll(allIssueTypes.findAll { it.name in [""Story"",""Bug"",""Enabler Story"",""Spike"",""Architecture Spike"",""Subtask""] })
}
 
issueTypeField.setFieldOptions(availableIssueTypes)","// Rewritten / ProjectRoleManager Substitution
let context = await getContext()
let projectKey = context.extension.project.key
let resRolesInProject = await makeRequest(`/rest/api/2/project/${projectKey}/roledetails?currentMember=True`)            
let myRolesInProject = resRolesInProject.body

// Rewritten / ComponentAccessor.constantsManager.allIssueTypeObjects Substitution            
let resIssueTypes = await makeRequest(`/rest/api/2/project/${projectKey}`)
let issueTypes = resIssueTypes.body.issueTypes
  
let availableIssueTypes = []
 
if (myRolesInProject.includes(""Administrators"")) {
    getFieldById(""issuetype"").setOptionsVisibility(issueTypes.map(it => it.id), true)
}
 
else {
    let removed = issueTypes.filter(it => it.name == ""Epic"").map(it => it.id)
    getFieldById(""issuetype"").setOptionsVisibility(removed, true)
}

"
"field: NPS Score
- readonly","let npsScore = getFieldById(""customfield_10363"") // Id for NPS Score
npsScore.setReadOnly(true)"
"def desc = getFieldById(""description"")

def defaultValue = """"""To provide an assessment Security in general needs to know if the data is personal and/or protected and what environment the data is being taken from and where it is being taken to.
- Example of the data and/or log:
- Do you expect the data to contain personal and/or protected information? (Personal information includes, Names, Email addresses IP addresses and User Agent strings)
- If so do you need the personal and/or protected components?
-Origin Environment: This is where the data is now.
-Destination Environment: This is where you plan to store and/or process the data once you get it.
-Comments: Anything not included here that is pertinent to the data being requested.
"""""".replaceAll(/    /, '')

if (! underlyingIssue?.description) { 
    desc.setFormValue(defaultValue)
}","let desc = getFieldById(""description"")

let defaultValue = `To provide an assessment Security in general needs to know if the data is personal and/or protected and what environment the data is being taken from and where it is being taken to.
- Example of the data and/or log:
- Do you expect the data to contain personal and/or protected information? (Personal information includes, Names, Email addresses IP addresses and User Agent strings)
- If so do you need the personal and/or protected components?
-Origin Environment: This is where the data is now.
-Destination Environment: This is where you plan to store and/or process the data once you get it.
-Comments: Anything not included here that is pertinent to the data being requested.
`.replaceAll(/    /, '')

if (!desc.getValue().toString()) { 
    desc.setValue(defaultValue)
}
"
"def desc = getFieldById(""description"")

def defaultValue = """"""Insert the following Post-Mortem related links:

* RCA document
* Impact Analysis
* Bluejeans Meeting
"""""".replaceAll(/    /, '')

if (! underlyingIssue?.description) { 
    desc.setFormValue(defaultValue)
}","let desc = getFieldById(""description"")

let defaultValue = `Insert the following Post-Mortem related links:

* RCA document
* Impact Analysis
* Bluejeans Meeting`.replaceAll(/    /, '')

if (!desc.getValue().toString()) { 
    desc.setValue(defaultValue)
}
"
,
"field: Extended Protection Segmentrs
- required","let field = getFieldById(""customfield_10185"") // ID for Extended Protection Segments
field.setRequired(true)"
,
,
"def desc = getFieldById(""description"")
 
def defaultValue = """"""As a Data Scientist:
I want to [  ]

Acceptance Criteria:
    * 
    * 
    * 
    
    """""".replaceAll(/    /, '')
 
if (!underlyingIssue?.description) { // <1>
    desc.setFormValue(defaultValue)
}","let desc = getFieldById(""description"")
 
let defaultValue = """"""As a Data Scientist:
I want to [  ]

Acceptance Criteria:
    * 
    * 
    * 
    
    """""".replaceAll(/    /, '')
 
if (!desc.getValue().toString()) { // <1>
    desc.setValue(defaultValue)
}
"
,
"field: Work Start Time
- readonly","let field = getFieldById(""customfield_10159"") // ID for Work Start Time
field.setReadOnly(true)"
"def desc = getFieldById(""description"")

def defaultValue = """"""Please provide the following information to request access to another team's Databricks data. You can find more information on the [Request Data access|https://confluence.integralads.com/display/DP/Request+Data+access] Confluence page. 

*Requesting Team Details:*
Team Name:
Team Lead:

*Domain owning the data(if you know):*
Domain Team Name :
Domain Lead :

*Data Requirements:*
What specific data do you need access to? Please provide details such as tables name, schema and catalog.
What is the purpose of accessing this data? Please describe the intended use.
Will you be generating additional datasets from this data to be used by other downstream processes?
What is the retention period for the data being generated?

*Additional Information:*
Is there any other information or context you would like to provide regarding this data access request?

*For {color:#de350b}sensitive data{color} access:*

*Additional Data Requirements:*
Does your domain's service principal need access to see unmasked data?
Which specific users need access to see unmasked data?
How long do these users need access to this unmasked data?

*Data Handling and Retention:*
How do you plan to ensure the security and confidentiality of the accessed data?
How do you plan to handle and store the accessed data?
Will you be deleting or anonymizing the data after its intended use?

"""""".replaceAll(/    /, '')

if (! underlyingIssue?.description) { 
    desc.setFormValue(defaultValue)
}","let desc = getFieldById(""description"")

let defaultValue = `Please provide the following information to request access to another team's Databricks data. You can find more information on the [Request Data access|https://confluence.integralads.com/display/DP/Request+Data+access] Confluence page. 
*Requesting Team Details:*
Team Name:
Team Lead:

*Domain owning the data(if you know):*
Domain Team Name :
Domain Lead :

*Data Requirements:*
What specific data do you need access to? Please provide details such as tables name, schema and catalog.
What is the purpose of accessing this data? Please describe the intended use.
Will you be generating additional datasets from this data to be used by other downstream processes?
What is the retention period for the data being generated?

*Additional Information:*
Is there any other information or context you would like to provide regarding this data access request?

*For {color:#de350b}sensitive data{color} access:*

*Additional Data Requirements:*
Does your domain's service principal need access to see unmasked data?
Which specific users need access to see unmasked data?
How long do these users need access to this unmasked data?

*Data Handling and Retention:*
How do you plan to ensure the security and confidentiality of the accessed data?
How do you plan to handle and store the accessed data?
Will you be deleting or anonymizing the data after its intended use?

`.replaceAll(/    /, '')

if (!desc.getValue().toString()) { 
    desc.setValue(defaultValue)
}
"
"field: Actual incident start
- required","let field = getFieldById(""customfield_10197"") // ID for Actual Incident Start
field.setRequired(true)"
"field: Aha Description
- readonly","let field = getFieldById(""customfield_10197"") // ID for Aha Description
field.setReadonly(true)"
sets default description for ib project,"
let desc = getFieldById(""description"")

let defaultValue = `Enter your idea in detail
`.replaceAll(/    /, '')

if (!desc.getValue().toString()) { 
    desc.setValue(defaultValue)
}
"
sets default description for taxonomy project,"
let desc = getFieldById(""description"")

let defaultValue = `Please include the original message from the client as well as your additional comments.""
`.replaceAll(/    /, '')

if (!desc.getValue().toString()) { 
    desc.setValue(defaultValue)
}

"